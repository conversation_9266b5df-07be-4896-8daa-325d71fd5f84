package iris

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"context"
	"github.com/stretchr/testify/assert"
	"sync"
	"testing"
	"time"
)

func mustParse(value string) time.Time {
	t, err := time.Parse("2006-15-04 15:04:05", value)
	if err != nil {
		panic(err)
	}
	return t
}

func TestAgentRunContext_GetTotalKnowledgeRecallTime(t *testing.T) {
	type fields struct {
		User                     entity.User
		UserInfo                 entity.UserInfo
		Config                   *config.AgentRunConfig
		PromptLoader             PromptLoader
		Environ                  *RunEnviron
		Parameters               map[entity.RuntimeParameterKey]any
		State                    *AgentRunState
		CtxStorage               ContextStorage
		ctx                      context.Context
		llm                      framework.LLM
		bus                      EventBus
		logger                   Logger
		publisher                *AgentEventPublisher
		uuid                     uuid.Generator
		cli                      RuntimeAPIClient
		background               BackgroundServiceManager
		workspace                Workspace
		artifacts                ArtifactService
		telemetry                Telemetry
		telemetryTracer          tracing.Tracer
		remoteToolExecutor       RemoteToolExecutor
		StartedAt                time.Time
		knowledgeRecallTimePairs []KnowledgeRecallTimePair
		knowledgeRecallMutex     sync.Mutex
	}
	tests := []struct {
		name   string
		fields fields
		want   time.Duration
	}{
		{
			name: "test1",
			fields: fields{
				knowledgeRecallTimePairs: []KnowledgeRecallTimePair{
					{StartTime: mustParse("2025-12-01 00:00:03"), EndTime: mustParse("2025-12-01 00:00:06")},
					{StartTime: mustParse("2025-12-01 00:00:00"), EndTime: mustParse("2025-12-01 00:00:03")},
				},
			},
			want: 6 * time.Second,
		},
		{
			name: "test2",
			fields: fields{
				knowledgeRecallTimePairs: []KnowledgeRecallTimePair{
					{StartTime: mustParse("2025-12-01 00:00:04"), EndTime: mustParse("2025-12-01 00:00:06")},
					{StartTime: mustParse("2025-12-01 00:00:00"), EndTime: mustParse("2025-12-01 00:00:03")},
				},
			},
			want: 5 * time.Second,
		},
		{
			name: "test3",
			fields: fields{
				knowledgeRecallTimePairs: []KnowledgeRecallTimePair{
					{StartTime: mustParse("2025-12-01 00:00:02"), EndTime: mustParse("2025-12-01 00:00:06")},
					{StartTime: mustParse("2025-12-01 00:00:00"), EndTime: mustParse("2025-12-01 00:00:03")},
				},
			},
			want: 6 * time.Second,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &AgentRunContext{
				User:                     tt.fields.User,
				UserInfo:                 tt.fields.UserInfo,
				Config:                   tt.fields.Config,
				PromptLoader:             tt.fields.PromptLoader,
				Environ:                  tt.fields.Environ,
				Parameters:               tt.fields.Parameters,
				State:                    tt.fields.State,
				CtxStorage:               tt.fields.CtxStorage,
				ctx:                      tt.fields.ctx,
				llm:                      tt.fields.llm,
				bus:                      tt.fields.bus,
				logger:                   tt.fields.logger,
				publisher:                tt.fields.publisher,
				uuid:                     tt.fields.uuid,
				cli:                      tt.fields.cli,
				background:               tt.fields.background,
				workspace:                tt.fields.workspace,
				artifacts:                tt.fields.artifacts,
				telemetry:                tt.fields.telemetry,
				telemetryTracer:          tt.fields.telemetryTracer,
				remoteToolExecutor:       tt.fields.remoteToolExecutor,
				StartedAt:                tt.fields.StartedAt,
				knowledgeRecallTimePairs: tt.fields.knowledgeRecallTimePairs,
				knowledgeRecallMutex:     tt.fields.knowledgeRecallMutex,
			}
			assert.Equalf(t, tt.want, c.GetTotalKnowledgeRecallTime(), "GetTotalKnowledgeRecallTime()")
		})
	}
}
