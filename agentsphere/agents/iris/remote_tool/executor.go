package remotetool

import (
	"context"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"github.com/pkg/errors"
)

type RemoteToolExecutor struct {
	uuid      uuid.Generator
	publisher *iris.AgentEventPublisher
	logger    iris.Logger
	toolCalls sync.Map
}

func NewRemoteToolExecutor(idGen uuid.Generator, publisher *iris.AgentEventPublisher, logger iris.Logger) *RemoteToolExecutor {
	return &RemoteToolExecutor{
		uuid:      idGen,
		publisher: publisher,
		logger:    logger,
		toolCalls: sync.Map{},
	}
}

func (r *RemoteToolExecutor) SubmitToolCallResults(res entity.SubmitToolCallResultRequest) (any, error) {
	r.logger.Infof("tool call results submitted: %s %s", res.CallID, res.Name)

	ch, ok := r.toolCalls.Load(res.CallID)
	if !ok {
		r.logger.Errorf("tool call record %s not found", res.CallID)
		return nil, nil
	}

	if ch != nil {
		typedCh, ok := ch.(chan entity.SubmitToolCallResultRequest)
		if ok {
			typedCh <- res
			r.logger.Infof("submitted tool call results to tool: %s", res.CallID)
		} else {
			// Should not happen.
		}
	} else {
		r.logger.Errorf("tool call record %s channel is nil", res.CallID)
		// Maybe tool call is already finished or timeout.
	}

	return nil, nil
}

func (r *RemoteToolExecutor) CallRemoteTool(ctx context.Context, id, stepID, tool string, parameters map[string]any) (map[string]any, error) {
	if len(id) == 0 {
		id = r.uuid.NewID()
	}
	r.logger.Infof("calling remote tool %s - %s", tool, id)

	ch := make(chan entity.SubmitToolCallResultRequest, 1)
	r.toolCalls.Store(id, ch)

	// Publish event to runtime server.
	r.publisher.ReportToolCallRequired(iris.EventAgentToolCallRequired{
		ID:         id,
		StepID:     stepID,
		Tool:       tool,
		Parameters: parameters,
	})

	defer func() {
		r.toolCalls.Delete(id)
	}()

	// Wait for server to submit results.
	select {
	case res := <-ch:
		r.logger.Infof("received tool call results: %s %s", res.CallID, res.Name)
		if res.Error != nil {
			r.logger.Errorf("tool call failed %s %s: %s", res.CallID, res.Name, *res.Error)
			return nil, errors.New(*res.Error)
		}
		r.publisher.ReportToolCallConfirmed(iris.EventToolCallConfirmed{
			ID:         r.uuid.NewID(),
			StepID:     stepID,
			ToolCallID: id,
			Action:     iris.ToolCallActionConfirm,
			Reason:     "received user input",
			Parameters: res.Results,
		})
		return res.Results, nil
	case <-ctx.Done():
		r.logger.Errorf("cancelled waiting for tool call results: %s %s", id, tool)
		r.publisher.ReportToolCallConfirmed(iris.EventToolCallConfirmed{
			ID:         r.uuid.NewID(),
			StepID:     stepID,
			ToolCallID: id,
			Action:     iris.ToolCallActionTimeout,
			Reason:     "timeout",
		})
		return nil, errors.WithMessage(ctx.Err(), "cancelled waiting for tool call results")
	}
}
