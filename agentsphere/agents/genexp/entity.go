package genexp

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience/filter"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
)

type TaskTrajectoryNode struct {
	UserMessage *iris.Message
	Round       *PlanActRound
}

type PlanActRound struct {
	Plan      Plan
	Execution Execution
}

type Plan struct {
	Rationale       string
	ProgressPlan    string
	Actor           string
	Persona         string
	TaskDescription string
	Tools           []string
	Experiences     []filter.ExperienceItem
}

type Execution struct {
	Evaluation string
	Output     string
	Reference  []ExecutionReference
	Detail     *ExecutionDetail
}

type ExecutionReference struct {
	ID       int            `mapstructure:"id"`
	Title    string         `mapstructure:"title"`
	URI      string         `mapstructure:"uri"`
	MetaData map[string]any `mapstructure:"meta_data"`
}

type ExecutionDetail struct {
	Task           string
	Tools          []prompt.ToolDescription
	ExecutionTrace string
	Context        Context
	Rounds         []ExecutionDetailRound
	Summarized     string
}

type Context struct {
	Knowledges   []knowledges.KnowledgeItem
	Experiences  []filter.ExperienceItem
	FormerOutput string
}

type ExecutionDetailRound struct {
	Think  Think
	Action Action
	Detail *ExecutionDetail // Agent tool execution detail
}

type Think struct {
	Rationale string `mapstructure:"rationale"`
}

type Action struct {
	Tool       string
	Parameters map[string]any
	Results    map[string]any
	Error      string
}
