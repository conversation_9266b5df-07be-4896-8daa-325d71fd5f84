package genexp

import (
	"encoding/json"
	"io"
	"os"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	agentraceparser "code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace/parser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience/filter"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/lib/conv"

	"code.byted.org/gopkg/pkg/errors"
	"github.com/samber/lo"
)

func getTaskTrajectory(planActRun *agentraceparser.TreeNode) ([]TaskTrajectoryNode, error) {
	items := make([]TaskTrajectoryNode, 0)
	msgs := make(map[string]bool)
	for _, child := range planActRun.Children {
		if child.Value.SpanName != "planact_round" {
			continue
		}
		var conversation iris.Conversation
		err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data["conversation"], &conversation)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to decode conversation")
		}
		lastUser, _, ok := lo.FindLastIndexOf(conversation.Messages, func(item *iris.Message) bool {
			return item.From == "user"
		})
		if ok && !msgs[lastUser.ID] {
			msgs[lastUser.ID] = true
			items = append(items, TaskTrajectoryNode{
				UserMessage: lastUser,
				Round:       nil,
			})
		}

		// 如果是有多个 execute 节点，说明是基于模版经验 SOP 执行的任务，没有 plan，直接执行多个 step，需要特殊解析
		if executeCount := lo.CountBy(child.Children, func(item *agentraceparser.TreeNode) bool {
			return item.Value.SpanName == "execute"
		}); executeCount > 1 {
			for _, child := range child.Children {
				if child.Value.SpanName != "execute" {
					continue
				}
				execution := struct {
					Actor  string `mapstructure:"actor"`
					Inputs struct {
						Task           string `mapstructure:"task"`
						Tools          string `mapstructure:"tools"`
						Persona        string `mapstructure:"persona"`
						ExecutionTrace string `mapstructure:"execution_trace"`
					} `mapstructure:"inputs"`
					Outputs struct {
						Evaluation string               `mapstructure:"evaluation"`
						Output     string               `mapstructure:"output"`
						Reference  []ExecutionReference `mapstructure:"reference"`
					} `mapstructure:"outputs"`
				}{}
				err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data, &execution)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to decode execution")
				}
				round := &PlanActRound{
					Plan: Plan{
						Rationale:       "",
						ProgressPlan:    "",
						Actor:           execution.Actor,
						Persona:         execution.Inputs.Persona,
						TaskDescription: execution.Inputs.Task,
						Tools: lo.Filter(
							strings.Split(execution.Inputs.Tools, ","),
							func(item string, index int) bool {
								return item != ""
							},
						),
					},
					Execution: Execution{
						Evaluation: execution.Outputs.Evaluation,
						Output:     execution.Outputs.Output,
						Reference:  execution.Outputs.Reference,
						Detail:     &ExecutionDetail{},
					},
				}
				if execution.Actor == "mewtwo" {
					round.Execution.Detail = getExecutionDetail(child)
				}
				items = append(items, TaskTrajectoryNode{
					Round:       round,
					UserMessage: nil,
				})
			}
			continue
		}

		var round PlanActRound
		for _, child := range child.Children {
			switch child.Value.SpanName {
			case "filter_experience_by_llm":
				var experiences []filter.ExperienceItem
				err := json.Unmarshal(conv.JSONBytes(child.Value.Data["filtered_experiences"]), &experiences)
				if err != nil {
					continue
				}
				round.Plan.Experiences = experiences
			case "plan":
				plan := struct {
					Agent      string `mapstructure:"agent"`
					Progress   string `mapstructure:"progress"`
					Rationale  string `mapstructure:"rationale"`
					Parameters struct {
						Persona string `mapstructure:"persona"`
						Task    string `mapstructure:"task"`
						Tools   string `mapstructure:"tools"`
					} `mapstructure:"parameters"`
				}{}
				err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data["plan"], &plan)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to decode plan")
				}
				round.Plan.Rationale = plan.Rationale
				round.Plan.ProgressPlan = plan.Progress
				round.Plan.Actor = plan.Agent
				round.Plan.Persona = plan.Parameters.Persona
				round.Plan.TaskDescription = plan.Parameters.Task
				round.Plan.Tools = lo.Filter(
					strings.Split(plan.Parameters.Tools, ","),
					func(item string, index int) bool {
						return item != ""
					},
				)
			case "execute":
				execution := struct {
					Evaluation string               `mapstructure:"evaluation"`
					Output     string               `mapstructure:"output"`
					Reference  []ExecutionReference `mapstructure:"reference"`
				}{}
				err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data["outputs"], &execution)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to decode execution")
				}
				round.Execution = Execution{
					Evaluation: execution.Evaluation,
					Output:     execution.Output,
					Reference:  execution.Reference,
				}
				if round.Plan.Actor == "mewtwo" {
					round.Execution.Detail = getExecutionDetail(child)
				}
			}
		}
		items = append(items, TaskTrajectoryNode{
			UserMessage: nil,
			Round:       &round,
		})
	}
	return items, nil
}

func getExecutionDetail(executeNode *agentraceparser.TreeNode) *ExecutionDetail {
	if executeNode == nil || len(executeNode.Children) == 0 {
		return nil
	}
	round := executeNode.Children[0]
	var detail ExecutionDetail
	executeData, _ := conv.MapToStructByJSONTag[struct {
		Inputs struct {
			Task           string `json:"task"`
			ExecutionTrace string `json:"execution_trace"`
		} `json:"inputs"`
	}](executeNode.Value.Data)
	detail.Task = executeData.Inputs.Task
	detail.ExecutionTrace = executeData.Inputs.ExecutionTrace
	mewtwoRoundData, _ := conv.MapToStructByJSONTag[struct {
		Knowledges []knowledges.KnowledgeItem `json:"knowledges"`
		Input      string                     `json:"input"`
		Tools      []prompt.ToolDescription   `json:"tools"`
	}](round.Value.Data)
	detail.Context.Knowledges = mewtwoRoundData.Knowledges
	detail.Tools = mewtwoRoundData.Tools
	// Fallback to old format if parsing failed.
	trace := strings.Split(mewtwoRoundData.Input, "[Execution Trace]")
	if len(trace) > 1 {
		if len(detail.Task) == 0 {
			detail.Task = strings.TrimSpace(trace[0])
		}
		if len(detail.ExecutionTrace) == 0 {
			detail.ExecutionTrace = strings.TrimSpace(trace[1])
		}
	}

	for _, child := range round.Children {
		switch child.Value.SpanName {
		case "filter_experience_by_llm":
			var experiences []filter.ExperienceItem
			err := json.Unmarshal(conv.JSONBytes(child.Value.Data["filtered_experiences"]), &experiences)
			if err != nil {
				continue
			}
			detail.Context.Experiences = experiences
		// case "retrieve_knowledge":
		// 	var knowledgeItems []knowledges.KnowledgeItem
		// 	err := json.Unmarshal(conv.JSONBytes(child.Value.Data["recalled"]), &knowledgeItems)
		// 	if err != nil {
		// 		panic(err)
		// 	}
		// 	detail.Context.Knowledges = append(detail.Context.Knowledges, knowledgeItems...)
		case "serial_agent_run_step":
			var step ExecutionDetailRound
			for _, child := range child.Children {
				switch {
				case child.Value.SpanName == "think":
					err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data["thought"], &step.Think)
					if err != nil {
						continue
					}
				case child.Value.SpanType == "tool_call":
					action := struct {
						Name    string         `mapstructure:"name"`
						Inputs  map[string]any `mapstructure:"inputs"`
						Outputs map[string]any `mapstructure:"outputs"`
					}{}
					err := conv.DecodeMapstructureWithTimeStringEnabled(child.Value.Data, &action)
					if err != nil {
						continue
					}
					step.Action = Action{
						Tool:       action.Name,
						Parameters: action.Inputs,
						Results:    action.Outputs,
					}
					// If Tool is Agent Tool we should parse child to step detail
					step.Detail = getExecutionDetail(child)
				}
			}
			detail.Rounds = append(detail.Rounds, step)
		}
	}
	return &detail
}

func ParseTraceFile(filename string) ([]TaskTrajectoryNode, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to open trace file")
	}
	defer file.Close()
	return ParseTraceFileWithReader(file)
}

func ParseTraceFileWithReader(reader io.Reader) ([]TaskTrajectoryNode, error) {
	tree, err := agentraceparser.ParseTree(reader)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse trace file")
	}

	var planActRun []*agentraceparser.TreeNode
	agentraceparser.WalkTree(tree, func(node *agentraceparser.TreeNode) error {
		if node.Value.SpanName == "planact_agent_run" {
			planActRun = append(planActRun, node)
			return agentraceparser.ErrWalkSkip
		}
		return nil
	})
	if len(planActRun) == 0 {
		return nil, errors.New("planact_agent_run node not found")
	}
	var trajectory []TaskTrajectoryNode
	for _, node := range planActRun {
		items, err := getTaskTrajectory(node)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get task trajectory")
		}
		trajectory = append(trajectory, items...)
	}
	return trajectory, nil
}
