package knowledges

import (
	"context"
	"fmt"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/security/zti-jwt-golang/ztijwt/logger"
	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

type KnowledgeItem struct {
	ID          string             `json:"id" yaml:"ID" mapstructure:"ID"`
	Category    KgRetrieveCategory `json:"category" yaml:"Category" mapstructure:"Category"`
	CategoryKey string             `json:"category_key" yaml:"CategoryKey" mapstructure:"CategoryKey"`
	Tags        []string           `json:"tags" yaml:"Tags" mapstructure:"Tags"`
	// Short title of the knowledge.
	Title string `json:"title" yaml:"Title" mapstructure:"Title"`
	// indicates if always use this knowledge.
	Pinned    bool   `json:"pinned" yaml:"Pinned" mapstructure:"Pinned"`
	EnabledIf string `json:"enabled_if" yaml:"EnabledIf" mapstructure:"EnabledIf"`
	// when to use this knowledge.
	UsedWhen string `json:"used_when" yaml:"UsedWhen" mapstructure:"UsedWhen"`
	// knowledge content.
	Content string `json:"content" yaml:"Content" mapstructure:"Content"`
}

type Knowledgebase interface {
	RetrieveKnowledge(ctx *iris.AgentRunContext, opt KgRetrieveOption) ([]KnowledgeItem, error)
	AllKnowledgeItems() []KnowledgeItem
}

const ScenarioStoreKey = "scenario_store"

const LarkTemplateScenarioKey = "lark_template"

type Scenario struct {
	Key    string            `json:"key" expr:"key"`
	SubKey string            `json:"sub_key" expr:"sub_key"`
	Tags   map[string]string `json:"tags" expr:"tags"`
}

type KgRetrieveStrategy string

const (
	KgRetrieveStrategyAccurate KgRetrieveStrategy = "accurate"
	KgRetrieveStrategyLLM      KgRetrieveStrategy = "llm"
	KgRetrieveStrategyVector   KgRetrieveStrategy = "vector"
)

type KgRetrieveCategory string

const (
	KgRetrieveCategoryUser     KgRetrieveCategory = "user"
	KgRetrieveCategorySystem   KgRetrieveCategory = "system"
	KgRetrieveCategoryScenario KgRetrieveCategory = "scenario"
	KgRetrieveCategoryTool     KgRetrieveCategory = "tool"
)

const (
	ToolTagBeforeAction string = "before_action"
	ToolTagUsage        string = "usage"
	ToolTagAfterAction  string = "after_action"
	ToolTagGlobal       string = "global"
)

type KgRetrieveOption struct {
	Query string
	// max number of knowledge items to retrieve.
	Limit    int
	Strategy KgRetrieveStrategy
	Category KgRetrieveCategory
	Param    RetrieveParam
	// 多次召回，取召回结果的并集
	Sampling int
	Timeout  time.Duration
}

type RetrieveParam struct {
	Agent        string   `expr:"agent"`
	Variant      string   `expr:"variant"`
	Tools        []string `expr:"tools"`
	WithCitation bool     `expr:"with_citation"`
	Scenario     Scenario `expr:"scenario"`
	Tag          string   `expr:"tag"`
	KnowledgeIDs []string `expr:"knowledge_ids"`
}

type SemanticKnowledgebase struct {
	llm                 framework.LLM
	localKnowledgeItems []Tuple
	config              iris.SceneModelConfig

	logger iris.Logger
}

type Tuple struct {
	Knowledge  KnowledgeItem
	compiledIf *vm.Program
}

func (s *SemanticKnowledgebase) AllKnowledgeItems() []KnowledgeItem {
	result := make([]KnowledgeItem, 0)
	for _, tuple := range s.localKnowledgeItems {
		result = append(result, tuple.Knowledge)
	}
	return lo.UniqBy(result, func(item KnowledgeItem) string {
		return item.ID
	})
}

var _ Knowledgebase = &SemanticKnowledgebase{}

func NewSemanticKnowledgebase(run *iris.AgentRunContext, knowledges []KnowledgeItem, llmConfig iris.SceneModelConfig) *SemanticKnowledgebase {
	tuples := make([]Tuple, 0)
	for i, knowledge := range knowledges {
		if len(knowledge.ID) == 0 {
			// Avoid ID collision.
			knowledge.ID = strconv.Itoa(len(knowledges) + i)
		}
		// Default to false.
		if knowledge.EnabledIf == "" {
			knowledge.EnabledIf = "false"
		}
		exp, err := expr.Compile(knowledge.EnabledIf, expr.Env(RetrieveParam{}), expr.AsBool())
		if err != nil {
			run.GetLogger().Warnf("failed to compile expr for knowledge: %s, error: %+v, if: %s", knowledge.Title, err, knowledge.EnabledIf)
		} else {
			tuple := Tuple{
				Knowledge:  knowledge,
				compiledIf: exp,
			}
			tuples = append(tuples, tuple)
		}
	}

	knowledgeSetConfig := conv.DefaultAny[*entity.KnowledgesetConfig](run.Parameters[entity.RuntimeParametersKnowledgeSetConfig])
	if knowledgeSetConfig == nil {
		return &SemanticKnowledgebase{
			llm:                 run.GetLLM(),
			localKnowledgeItems: tuples,
			config:              llmConfig,
			logger:              run.GetLogger(),
		}
	}
	for _, knowledgeSet := range knowledgeSetConfig.Knowledgesets {
		id := knowledgeSet.KnowledgesetVersionID
		knowledgeSetType := knowledgeSet.KnowledgesetType
		if knowledgeSetType != string(KgRetrieveCategoryScenario) {
			// 平台侧只支持 scenario 类型的
			run.GetLogger().Errorf("unsupported knowledgeset type: %s", knowledgeSetType)
			continue
		}
		knowledgeList, err := run.GetAPIClient().ListKnowledge(run, &nextagent.ListKnowledgeRequest{
			KnowledgesetVersionID: id,
		})
		if err != nil {
			run.GetLogger().Errorf("failed to list knowledge for knowledgeset: %s, error: %+v", id, err)
			continue
		}
		setInfo, err := run.GetAPIClient().GetKnowledgeSetInfo(run, []string{knowledgeSet.KnowledgesetID})
		if err != nil || len(setInfo.Knowledgesets) == 0 {
			run.GetLogger().Errorf("failed to get knowledgeset info for knowledgeset: %s, error: %+v", id, err)
			continue
		}

		// to map
		setKey := setInfo.Knowledgesets[0].Key
		for _, knowledge := range knowledgeList.GetKnowledges() {
			item := KnowledgeItem{
				ID:          setKey + "." + knowledge.Key,
				Tags:        knowledge.Tags,
				Category:    KgRetrieveCategory(knowledgeSetType),
				CategoryKey: setKey,
				Title:       knowledge.Title,
				EnabledIf:   knowledge.EnableIf,
				UsedWhen:    knowledge.UseWhen,
				Content:     knowledge.Content,
			}
			if item.EnabledIf == "" {
				item.EnabledIf = "false"
			}
			exp, err := expr.Compile(item.EnabledIf, expr.Env(RetrieveParam{}), expr.AsBool())
			if err != nil {
				run.GetLogger().Errorf("failed to compile expr for knowledge: %s, error: %+v, if: %s", item.Title, err, item.EnabledIf)
				continue
			}

			tuples = append(tuples, Tuple{
				Knowledge:  item,
				compiledIf: exp,
			})
		}
	}

	return &SemanticKnowledgebase{
		llm:                 run.GetLLM(),
		localKnowledgeItems: tuples,
		config:              llmConfig,
		logger:              run.GetLogger(),
	}
}

func (s *SemanticKnowledgebase) recallByLLM(
	ctx context.Context,
	opt KgRetrieveOption,
	knowledges []KnowledgeItem,
) (recalled []KnowledgeItem, thought *iris.Thought, err error) {
	tag := string(lo.Ternary(opt.Category != "", "retrieve_knowledge_"+opt.Category, "retrieve_knowledge"))
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).
		StartCustomSpan(
			ctx,
			agentrace.SpanTypeStep,
			"retrieve_knowledge_by_llm",
		)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"recalled": recalled,
		}))
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()

	nonPinned := lo.Filter(knowledges, func(item KnowledgeItem, _ int) bool {
		return !item.Pinned && strings.TrimSpace(item.UsedWhen) != ""
	})
	recalled = lo.Filter(knowledges, func(item KnowledgeItem, _ int) bool {
		return item.Pinned
	})
	if len(nonPinned) == 0 {
		return recalled, nil, nil
	}
	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(KnowledgeSystemPromptTemplate, map[string]any{"Knowledges": nonPinned}),
		prompt.WithUserMessage(KnowledgeUserPromptTemplate, map[string]any{"Task": opt.Query}),
	})
	if err != nil {
		return recalled, nil, errors.WithMessage(err, "failed to compose messages")
	}

	logger.Debugf("knowledge retrieve messages:\n %+v", messages)

	llmOpt := framework.LLMCompletionOption{
		Model:       "deepseek-v3-250324",
		Temperature: 0.3,
		MaxTokens:   500,
		Tag:         tag,
	}
	if s.config.Model != "" {
		llmOpt.Model = s.config.Model
		llmOpt.MaxTokens = s.config.MaxTokens
		llmOpt.Temperature = s.config.Temperature
		llmOpt.Thinking = s.config.Thinking
	}
	llmSpan, ctx := agentrace.GetRuntimeTracerFromContext(ctx).
		StartCustomSpan(
			ctx,
			agentrace.SpanTypeLLMCall,
			"retrieve_knowledge",
			agentrace.WithObjectSpanData(map[string]any{
				"prompt_messages": messages,
			}),
		)
	defer llmSpan.Finish()
	retryCount := 0
retry:
	res, err := s.llm.ChatCompletion(ctx, messages, llmOpt)
	if err != nil {
		agentrace.AddErrorTag(llmSpan, err)
		return recalled, nil, errors.WithMessage(err, "failed to call llm")
	}
	llmSpan.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"llm_call": res,
	}))
	logger.Debugf("retrieve result:\n%s\n", res.Content)
	thought = &iris.Thought{
		Content: res.Content,
		LLMCall: iris.LLMCall{
			ModelName:    llmOpt.Model,
			Temperature:  float64(llmOpt.Temperature),
			Usage:        res.TokenUsage,
			Prompt:       messages,
			FinishReason: string(res.FinishReason),
		},
	}

	topTags, err := prompt.ParseTopTagsV2(res.Content)
	// if response is incomplete, but some tags are found, we still can use them
	if err != nil && len(topTags) == 0 {
		return recalled, thought, errors.WithMessage(err, "failed to parse top tags")
	}

	rationales := make(map[string]int)
	maxRepeated := 0
	for _, tag := range topTags {
		if tag.XMLName.Local == "rationale" {
			rationales[tag.Content]++
			maxRepeated = max(maxRepeated, rationales[tag.Content])
		}
		if tag.XMLName.Local != "id" {
			rationales[tag.Content]++
			maxRepeated = max(maxRepeated, rationales[tag.Content])
			continue
		}
		id := strings.TrimSpace(tag.Content)
		if id == "" {
			continue
		}
		item, ok := lo.Find(nonPinned, func(item KnowledgeItem) bool {
			return item.ID == id
		})
		if !ok {
			continue
		}
		recalled = append(recalled, item)
	}
	// 存在重复，模型抽风，重试
	if maxRepeated >= 2 && len(recalled) >= 10 {
		if retryCount < 3 {
			retryCount++
			goto retry
		}
	}

	return recalled, thought, nil
}

func (s *SemanticKnowledgebase) prefilter(ctx context.Context, opt KgRetrieveOption) []KnowledgeItem {
	items := make([]KnowledgeItem, 0)

	for _, knowledge := range s.localKnowledgeItems {
		// 前置过滤规则
		if len(opt.Param.KnowledgeIDs) > 0 {
			// 如果指定了knowledge id，那么直接返回匹配id的知识，不做任何其他规则校验
			if lo.Contains(opt.Param.KnowledgeIDs, knowledge.Knowledge.ID) {
				items = append(items, knowledge.Knowledge)
			}
			continue
		}
		if opt.Param.Tag != "" {
			if !lo.Contains(knowledge.Knowledge.Tags, opt.Param.Tag) {
				continue
			}
		}
		if opt.Category != "" && opt.Category != knowledge.Knowledge.Category {
			continue
		}
		if opt.Category == KgRetrieveCategoryScenario {
			scenario := opt.Param.Scenario
			if scenario.Key != "" && knowledge.Knowledge.CategoryKey != scenario.Key {
				continue
			}
			// 如果指定了subkey，那么只要规则通过，则固定返回匹配key的知识
			if scenario.SubKey != "" && (scenario.Key+"."+scenario.SubKey) == knowledge.Knowledge.ID {
				s.logger.Infof("matched the subkey of scenario: %v, knowledge ID:%s", scenario, knowledge.Knowledge.ID)
				// 这是无指针数组的一份copy数据，所以不会改变map中的数据
				knowledge.Knowledge.Pinned = true
			}
		} else if opt.Category == KgRetrieveCategoryTool {
			if len(opt.Param.Tools) > 0 {
				if !lo.ContainsBy(opt.Param.Tools, func(item string) bool { return item == knowledge.Knowledge.CategoryKey }) {
					continue
				}
			}
		}

		// 后置表达式过滤
		if knowledge.compiledIf != nil {
			enabled, err := expr.Run(knowledge.compiledIf, opt.Param)
			if err != nil {
				s.logger.Warnf("failed to run expr for knowledge: %s, error: %+v, if: %s", knowledge.Knowledge.Title, err, knowledge.Knowledge.EnabledIf)
				continue
			}
			if enabled == false {
				continue
			}
		}
		items = append(items, knowledge.Knowledge)
	}
	return items
}

// RetrieveKnowledge implements Knowledgebase.
func (s *SemanticKnowledgebase) RetrieveKnowledge(c *iris.AgentRunContext, opt KgRetrieveOption) (recalled []KnowledgeItem, err error) {
	span, ctx := c.GetTracer(c).
		StartCustomSpan(
			c,
			agentrace.SpanTypeStep,
			"retrieve_knowledge",
			agentrace.WithObjectSpanData(map[string]any{
				"query":    opt.Query,
				"limit":    opt.Limit,
				"sampling": opt.Sampling,
			}),
		)
	// add a default timeout to prevent llm call from hanging too long
	ctx, cancel := context.WithTimeout(ctx, lo.Ternary(opt.Timeout > 0, opt.Timeout, 30*time.Second))
	defer cancel()
	start := time.Now()

	defer func() {
		endTime := time.Now()
		duration := endTime.Sub(start)

		// Record this knowledge recall time pair
		c.RecordKnowledgeRecallTime(start, endTime)

		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"recalled": recalled,
		}))
		agentrace.AddErrorTag(span, err)
		span.Finish()
		telemetry.EmitKnowledgeRecall(c, "recalled", opt.Param.Agent, opt.Param.Scenario.Key, opt.Param.Tools, string(opt.Strategy), string(opt.Category), len(recalled), duration)
	}()
	// 项目空间场景，召回项目空间知识输入用户提示词中
	if entity.CheckParamIsProjectSpace(c.Parameters[entity.RuntimeParametersSpaceInfomation]) && opt.Category == KgRetrieveCategorySystem {
		if !lo.Contains(opt.Param.KnowledgeIDs, "system.projectspace_default_prompt") {
			opt.Param.KnowledgeIDs = append(opt.Param.KnowledgeIDs, "system.projectspace_default_prompt")
		}
		s.logger.Infof("[RetrieveKnowledge]:[project_space_logic]: after prefilter knowledges, the knowledge_ids are:%v", opt.Param.KnowledgeIDs)
	}

	knowledges := s.prefilter(ctx, opt)
	telemetry.EmitKnowledgeRecall(c, "prefilter", opt.Param.Agent, opt.Param.Scenario.Key, opt.Param.Tools, string(opt.Strategy), string(opt.Category), len(knowledges), time.Since(start))
	if opt.Category == KgRetrieveCategoryTool {
		globalKnowledgeKey := fmt.Sprintf("<type:%s>", ToolTagGlobal)
		globalKnowledges := iris.RetrieveStoreByKey[[]KnowledgeItem](c, globalKnowledgeKey)
		s.logger.Infof("global tool knowledge: %+v", lo.Map(globalKnowledges, func(item KnowledgeItem, _ int) string { return item.ID }))
		knowledges = append(knowledges, globalKnowledges...)
		defer func() {
			// special logic, save global tool knowledge to agent run context
			toolGlobal := lo.Filter(recalled, func(item KnowledgeItem, _ int) bool {
				return lo.Contains(item.Tags, ToolTagGlobal)
			})

			// todo
			//afterActionToolKnowledge := lo.Filter(recalled, func(item KnowledgeItem, _ int) bool {
			//	return lo.Contains(item.Tags, ToolTagAfterAction)
			//})
			globalKnowledges = append(globalKnowledges, toolGlobal...)
			globalKnowledges = lo.UniqBy(globalKnowledges, func(item KnowledgeItem) string {
				return item.ID
			})
			if len(globalKnowledges) > 0 {
				iris.UpdateStoreByKey(c, globalKnowledgeKey, globalKnowledges)
				s.logger.Infof("Update global tool knowledge: %+v", lo.Map(globalKnowledges, func(item KnowledgeItem, _ int) string { return item.ID }))
			}
		}()
	}

	if len(knowledges) == 0 {
		s.logger.Infof("no knowledge found for strategy: %s option %+v", opt.Category, opt.Param)
		return []KnowledgeItem{}, nil
	}

	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"prefiltered": knowledges,
	}))

	switch opt.Strategy {
	case KgRetrieveStrategyAccurate:
		return knowledges, nil
	case KgRetrieveStrategyLLM:
		return s.sampleRecallByLLM(c, ctx, opt, knowledges)
	default:
		return s.sampleRecallByLLM(c, ctx, opt, knowledges)
	}
}

func (s *SemanticKnowledgebase) sampleRecallByLLM(c *iris.AgentRunContext, ctx context.Context, opt KgRetrieveOption, knowledges []KnowledgeItem) ([]KnowledgeItem, error) {
	if len(opt.Query) == 0 {
		s.logger.Infof("skip knowledge retrieval for empty query")
		return []KnowledgeItem{}, nil
	}

	// The returned ctx is canceled if the first call to g.Go returns a non-nil error.
	// But here we don't care about the error, so we ignore the returned ctx.
	g, _ := errgroup.WithContext(ctx)
	if opt.Sampling < 1 {
		opt.Sampling = 1
	}
	g.SetLimit(3)
	lock := &sync.Mutex{}

	recalled := make([]KnowledgeItem, 0, opt.Limit)
	thoughts := make([]*iris.Thought, opt.Sampling)
	for i := 0; i < opt.Sampling; i++ {
		g.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					s.logger.Errorf("recover from panic %+v, stack: %s", r, debug.Stack())
				}
			}()
			items, thought, err := s.recallByLLM(ctx, opt, knowledges)
			thoughts[i] = thought
			if err != nil {
				return err
			}
			lock.Lock()
			defer lock.Unlock()
			recalled = append(recalled, items...)
			return nil
		})
	}
	if err := g.Wait(); err != nil && len(recalled) == 0 {
		return nil, err
	}

	// Report think if possible.
	for _, thought := range thoughts {
		if thought == nil {
			continue
		}
		// Avoid to overwrite original thought.
		c.GetPublisher().ReportThoughtEvent("", thought)
	}

	// 被选择次数多的知识排在前面
	groups := lo.Values(
		lo.GroupBy(
			recalled,
			func(item KnowledgeItem) string {
				return item.ID
			},
		),
	)
	sort.Slice(groups, func(i, j int) bool {
		return len(groups[i]) > len(groups[j])
	})
	recalled = make([]KnowledgeItem, len(groups))
	if opt.Limit > 0 && len(groups) > opt.Limit {
		// 如果召回的知识数量超过限制，则只保留前 opt.Limit 个
		s.logger.Infof("recall knowledge limit exceeded, limit: %d, actual: %d", opt.Limit, len(groups))
		groups = groups[:opt.Limit]
	}
	for idx, group := range groups {
		recalled[idx] = group[0]
	}

	return recalled, nil
}
