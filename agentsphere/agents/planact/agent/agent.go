package planact_agent

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/panics"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/projectartifact"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	mcpserver "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/mcp"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/devai"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/a2acaller"
	reporteractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/reporter"
	responseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/prompts"
	queryrecognizer "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/query_preprocessor"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	agentutil "code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	agspentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/lib/util"
)

const (
	// v1
	AgentIDV1       = "planact_agent"
	ReplanActorName = "replan"

	// v1.5
	AgentIDV1_5    = "dynamic_planact_agent"
	PreprocessorID = "preprocessor"
)

type PlanActAgent struct {
	iris.AgentInfo
	planner      GeneralPlanner
	actorFactory *ActorFactory

	// NOTE: the properties added here WILL NOT be persisted to the storage and will get lost after restart.

	executionTimer *agentutil.Timer
	// websearchReportFileName is used to deduplicate the report file name.
	websearchReportFileName map[string]bool
	knowledgebase           knowledges.Knowledgebase
	kgCache                 *sync.Map
}

func (a *PlanActAgent) ValidateConfig(config *config.AgentRunConfig) error {
	if config == nil {
		return fmt.Errorf("config is not provided")
	}
	return nil
}

func NewPlanActAgent(id string) *PlanActAgent {
	factory := NewActorFactory(lo.Ternary(id == AgentIDV1_5, true, false))

	planAgent := &PlanActAgent{
		AgentInfo: iris.AgentInfo{
			Identifier: id,
			Desc:       "a general plan&act agent",
		},
		planner:                 nil, // initialized in `Run()`.
		actorFactory:            factory,
		websearchReportFileName: make(map[string]bool),
	}
	return planAgent
}

func (a *PlanActAgent) Run(originalRun *iris.AgentRunContext) (err error) {
	a.executionTimer = agentutil.NewTimer()
	a.executionTimer.Start()
	validateScenesConfig(originalRun)
	publisher, logger := originalRun.GetPublisher(), originalRun.GetLogger()

	a.knowledgebase = knowledges.CreateKnowledgebase(originalRun)
	a.kgCache = &sync.Map{}

	a2aVersions, ok := originalRun.Parameters[agspentity.RuntimeParameterA2AAgentVersions]
	if ok {
		// TODO get agent cards from server
		cards := conv.DecodeJSON[[]a2acaller.AgentCard](conv.JSONString(a2aVersions))
		for _, card := range cards {
			a.actorFactory.Register(card.Name, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
				return a2acaller.New(a2acaller.CreateOption{
					Card:      card,
					Version:   card.ID, // TODO use the real file version
					ActorStep: opt.ActorStep,
				})
			})
		}
	}

	tracer := originalRun.GetTracer(originalRun)

	planactSpan, ctx := tracer.
		StartCustomSpan(
			originalRun,
			agentrace.SpanTypeStep,
			"planact_agent_run",
			agentrace.WithObjectSpanData(
				map[string]any{
					"parameters": originalRun.Parameters,
				},
			),
		)
	originalRun = originalRun.WithContext(ctx)
	defer func() {
		planactSpan.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"conversation": originalRun.State.Conversation,
		}))
		planactSpan.Finish()
	}()

	a.planner = a.loadPlanner(originalRun)
	InitializeBackgroundServices(originalRun)
	actorDesc := a.initActorsDesc(originalRun)
	planactSpan.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"actors": actorDesc, // also record the available actors desc.
	}))

	var (
		// 创建一个新 run，方便注入 span
		// 不会被用户打断，但是会被 AbortAgentRun 等更上层的信号打断
		run = originalRun.WithContext(originalRun)
		// 会因用户打断而被 cancel
		cancelableSubRun *iris.AgentRunContext
		// 打断当前 plan-act round
		cancelRound context.CancelFunc
	)
	resetRoundCancel := func() {
		if cancelRound != nil {
			cancelRound()
		}
		subCtx, cancel := context.WithCancel(run)
		cancelRound = cancel
		cancelableSubRun = run.WithContext(subCtx)
		a.executionTimer.Reset()
		a.executionTimer.Start()
	}
	resetRoundCancel()
	defer cancelRound()

	// 是否应该进入空闲状态，用于区分被打断时是进入下一轮还是进入空闲状态
	beIdle := false
	// 是否被取消，用于判断是否需回复用户
	isCanceled := false

	// 用户取消当前任务，预期进入空闲状态
	run.State.Signals.RegisterCallback(func(s iris.Signal) {
		logger.Infof("receive signal: %s", s)
		if s == iris.SignalCancelAgentRun {
			beIdle = true
			isCanceled = true
			cancelRound()
		}
	})
	// 用户发送新消息，取消当前任务，重新生成计划
	run.State.Conversation.RegisterUpdateCallback(func() {
		logger.Info("conversation is updated, canceling sub agents")
		cancelRound()
	})

	// 如果是重启， 发一个重启事件
	if restart, _ := originalRun.CtxStorage.IsRestart(); restart {
		run.GetPublisher().ReportRestart(originalRun.State.RunID)
	}

	firstRestartRun, _ := run.CtxStorage.IsRestart()
	run.GetTelemetryTracer().SetGlobalTag("session_id", run.State.SessionID)
	// task 单轮开始时间
	roundStartTime := time.Now()
	round := 0
	for {
		if !firstRestartRun {
			err = a.runPlanActRound(run, cancelableSubRun)
			if err != nil {
				logger.Infof("plan act round failed: %v", err)
				switch {
				// 任务被终止，直接退出
				case run.Err() != nil:
					return err
				// 任务被用户打断，进入下一轮，或者进入空闲状态
				case cancelableSubRun.Err() != nil:
					metrics.AR.PlannerTaskTimeCost.WithTags(&metrics.PlannerTaskTag{
						FinishReason: "interrupted",
					}).Observe(float64(a.executionTimer.ElapsedTime().Microseconds()))
					logger.Infof("interrupted by user, task time cost: %v", a.executionTimer.ElapsedTime())
					if beIdle {
						beIdle = false
						resetRoundCancel()
						break
					}
					resetRoundCancel()
					continue
				// 因为错误终止，但是该错误可被重试，进入下一轮
				case iris.IsRecoverable(err):
					continue
				// 任务正常结束，进入空闲状态
				case iris.IsCompleted(err):
					break
				// 因为错误终止，但是该错误不可被重试，直接退出
				default:
					return err
				}
			} else {
				continue
			}

			// Emit total knowledge recall time after agent execution completes
			totalKnowledgeRecallTime := cancelableSubRun.GetTotalKnowledgeRecallTime()
			// 打印的日志中，添加 round 信息
			logger.Infof("round %d total knowledge recall time: %v", round, totalKnowledgeRecallTime)
			telemetry.EmitKnowledgeRecallTotal(run, round, totalKnowledgeRecallTime)

			// Reset knowledge recall timing at the start of agent execution
			run.ResetKnowledgeRecallTiming()
			logger.Infof("reset knowledge recall timing")

			// report task time
			logger.Info("the current task is completed, planact agent is idle")
			taskDuration := time.Since(roundStartTime)
			telemetry.EmitTaskExecution(run, round, taskDuration)
			logger.Infof("round %d task duration: %v", round, taskDuration)
			if isCanceled {
				isCanceled = false
				sendCanceledMessageToUser(run)
			}
			publisher.ReportAgentIdle()
		} else {
			firstRestartRun = false
		}
		// 空闲状态，等待任务结束信号或者用户发送新指令（消息）
		metrics.AR.PlannerTaskTimeCost.WithTags(&metrics.PlannerTaskTag{
			FinishReason: "idle",
		}).Observe(float64(a.executionTimer.ElapsedTime().Microseconds()))
		logger.Infof("task idle time cost: %v", a.executionTimer.ElapsedTime())
		ticker := time.NewTicker(time.Millisecond * 100)
	idleLoop:
		for {
			select {
			case <-run.Done():
				ticker.Stop()
				return nil
			case <-ticker.C:
				if run.State.Conversation.HasNewMessage() {
					ticker.Stop()
					// 这里可能存在并发问题，可能这里比 Conversation Callback 先执行，导致这里 reset 之后才收到 cancel 信号，导致 reset 无效，这里等待一会
					select {
					case <-time.After(time.Millisecond * 500):
						logger.Infof("wait for new message cancel timeout")
					case <-cancelableSubRun.Done():
						logger.Infof("recv run cancel due to new message, reset it")
					}
					resetRoundCancel()
					roundStartTime = time.Now()
					round++
					break idleLoop
				}
				continue
			}
		}
	}
}

func (a *PlanActAgent) runPlanActRound(
	run *iris.AgentRunContext, cancelableSubRun *iris.AgentRunContext,
) (err error) {
	logger, tracer := run.GetLogger(), run.GetTracer(run)

	span, ctx := tracer.
		StartCustomSpan(
			cancelableSubRun,
			agentrace.SpanTypeStep,
			"planact_round",
			agentrace.WithObjectSpanData(
				map[string]any{
					"conversation": run.State.Conversation,
				},
			),
		)
	cancelableSubRun = cancelableSubRun.WithContext(ctx)

	// 这个 span 要同时注入给 run，因为后续的 span 应该都是 planact_round 的子 span
	run = run.WithContext(agentrace.SetSpan(run, span))

	roundStart := time.Now()
	defer func() {
		lastRoundTimeCost := time.Since(roundStart)
		logger.Infof("round time cost: %v", lastRoundTimeCost)
		_ = metrics.AR.PlannerRoundTimeCost.Observe(float64(lastRoundTimeCost.Microseconds()))

		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()

	var skipPlan bool
	if run.State.Conversation.HasNewMessage() {
		logger.Info("new message received")
		// 在 preprocess 之前，先记录本次处理的是哪些消息，防止用户打断导致新消息被标记已处理
		dirtyMessages := run.State.Conversation.GetDirtyMessage()
		for _, m := range dirtyMessages {
			run.GetTelemetryTracer().StartSpan(run, "user_message", map[string]interface{}{
				"type":        tracing.SpanEventQuery,
				"message":     m.Content,
				"attachments": m.Attachments,
			}, tracing.SpanTypeEvent, nil)
		}
		preprocessQueryResult, err := a.preprocessQuery(cancelableSubRun)
		if err != nil {
			logger.Errorf("failed to preprocess query: %v", err)
		}
		skipPlan = preprocessQueryResult.DetectionResult.Intercepted
		logger.Infof("preprocess query result: %s", conv.JSONString(preprocessQueryResult))
		// 只要不是尝试套取 system prompt 的恶意消息，就存到 store 中，否则会丢失上下文
		if !preprocessQueryResult.DetectionResult.Harmful {
			a.updatePlannerRequirements(run, dirtyMessages)
			a.updateStore(run, preprocessQueryResult)
		} else {
			run.State.Conversation.MarkDeprecated(preprocessQueryResult.DetectionResult.ReplyTo)
		}
		// 如果消息不执行，则不重置最大轮数限制
		if !preprocessQueryResult.DetectionResult.Intercepted {
			// 重置失败计数状态并且提高最大轮数限制
			a.planner.ResetRound(cancelableSubRun, ResetRoundOption{
				ExpandMaxSteps: 20,
			})
		}
		if cancelableSubRun.Err() != nil {
			logger.Warnf("interrupted by user")
			return err
		}
	} else {
		logger.Infof("no new message, skip plan: %v, messages: %v", skipPlan, conv.JSONFormatString(run.State.Conversation.Messages))
	}
	// 可能会 skip plan，保存一次上下文
	a.storeContext(ctx, run)
	if !skipPlan {
		// generate plan
		logger.Infof("generating plan...")
		step, err := a.getNextStep(cancelableSubRun, logger)
		if err != nil {
			logger.Infof("failed to get next step: %v", err)
			if cancelableSubRun.Err() != nil {
				logger.Infof("interrupted by user and got plan error: %v", err)
			}
			return err
		}

		// execute the plan
		switch a.Name() {
		case AgentIDV1_5:
			err = a.executeSingleStep(cancelableSubRun, step)
			// log
			logger.Infof("execute single step knowledge time: %v", cancelableSubRun.GetTotalKnowledgeRecallTime())
			if err != nil {
				return err
			}
			a.storeContext(ctx, run)
			// 需要继续 plan-act
			return nil
		default:
			err = a.executePlan(cancelableSubRun, step)
			if err != nil {
				return err
			}
			a.storeContext(ctx, run)
			// check to replan
			_, replan := lo.Find(a.getPlan(step).Steps, func(step entity.PlanStep) bool {
				return step.ActorName == ReplanActorName
			})
			if replan {
				logger.Infof("continue to replan")
				return nil
			}
		}
	}

	return iris.NewCompleted(nil)
}

type PreprocessQueryResult struct {
	DetectionResult            agspentity.AckResult
	RelatedKnowledge           string
	PreprocessedItems          []queryrecognizer.PreprocessedMentionItem
	EnhancedRequirements       string
	RelatedWorkspaceKnowledges []string
	ExperiencePrompt           string
}

func (a *PlanActAgent) preprocessQuery(run *iris.AgentRunContext) (result PreprocessQueryResult, err error) {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	publisher, logger := run.GetPublisher(), run.GetLogger()

	messages := run.State.Conversation.GetDirtyMessage()

	// 1. extract urls and mentions
	queryRecognizer := queryrecognizer.URIRecognizer{}
	queryRecognizer.Recognize(run, messages)
	// 2. rewrite @mention to prompt string, this should be done after url recognition to prevent rewritten mention being recognized again
	err = queryrecognizer.RewriteMentions(messages)
	if err != nil {
		logger.Errorf("failed to rewrite mentions: %v", err)
	}
	logger.Infof("rewritten messages: %+v", messages)

	// last user requirements
	lastRequirements := a.getLastRequirements(run, messages)
	// requirements of historical conversation, before updatePlannerRequirements
	historyRequirements := store.Requirements
	wg := conc.NewWaitGroup()

	// retrieve related knowledge by knowledge base
	wg.Go(func() {
		knowledges, err := devai.GetRelatedKnowledge(lastRequirements)
		// we don't update store here as it may cause data race
		if err == nil && knowledges != nil && len(knowledges.RecallSegments) > 0 {
			result.RelatedKnowledge = knowledges.RecallSegments[0].Content
		}
	})
	wg.Go(func() {
		knowledges, err := websearch.RecallKnowledgebase(run, lastRequirements, 3)
		if err != nil {
			logger.Errorf("failed to recall knowledgebase: %v", err)
			return
		}
		result.RelatedWorkspaceKnowledges = knowledges
	})

	// enhance user requirements asynchronously
	var enhancedRequirements string
	wg.Go(func() {
		// only enhance when this is the initial requirements (first time processing)
		if store.InitialRequirements == "" {
			enhanced := enhanceRequriement(run, lastRequirements)
			if enhanced != "" {
				enhancedRequirements = enhanced
			}
		}
	})

	// 经验召回 - 使用 PlannerApplyType
	wg.Go(func() {
		if !experience.EnabledExpInsights(run) {
			logger.Infof("experience insights is disabled for planner")
			return
		}
		run.GetLogger().Infof("retrieving experience for planner newbie variant with task: %s", lastRequirements)

		// 创建经验召回器
		experienceRetriever, err := experience.NewExperienceRetriever()

		if err != nil {
			run.GetLogger().Errorf("failed to create experience retriever: %s", err)
			return
		}

		// 定义要检索的经验类型
		experienceTypes := []string{
			experience.Insights,
			experience.InsightsBadCase,
		}

		experiencePrompt, _, err := experienceRetriever.RetrieveInsights(
			run,
			lastRequirements,
			"",
			experience.PlannerApplyType,
			experienceTypes,
			experience.DefaultLimitExperience,
			[]string{},
		)
		if err != nil {
			run.GetLogger().Errorf("failed to retrieve planner experience: %s", err)
		} else if experiencePrompt != "" {
			run.GetLogger().Infof("retrieved planner experience prompt: %s", experiencePrompt)
			result.ExperiencePrompt = experiencePrompt
		} else {
			run.GetLogger().Infof("no relevant planner experience found")
		}
	})

	// Acknowledge user message first.
	// 1. the server has finished intent detection before runtime starts (no prepared cubes)
	// it should come with run parameters
	detect := make(chan struct{})
	go panics.Try(func() {
		defer close(detect)
		if run.Parameters[agspentity.RuntimeParametersDetectionResult] != nil {
			var detectionResult agspentity.AckResult
			err := mapstructure.Decode(run.Parameters[agspentity.RuntimeParametersDetectionResult], &detectionResult)
			if err != nil {
				logger.Errorf("failed to decode intent detection result: %v", err)
			} else {
				result.DetectionResult = detectionResult
			}
			logger.Infof("reuse server intent detection result: %+v", detectionResult)
		} else {
			// 2. the server has not finished intent detection before the agent run starts (use prepared cubes & docker)
			// Ack 暂定不能被打断，不然会导致 detect 失效，进而导致危险或者无效消息进入历史记录，导致后续任务被带偏
			result.DetectionResult.Harmful, result.DetectionResult.Intercepted, result.DetectionResult.ReplyTo, result.DetectionResult.Response = responseactor.AckAndRespond(run)
		}
		logger.Infof("skip plan: %v, send message [response to user]: %s", result.DetectionResult.Intercepted, result.DetectionResult.Response)
		publisher.ReportMessage(iris.EventAgentMessage{
			ID:      uuid.New().String(),
			ReplyTo: result.DetectionResult.ReplyTo,
			Content: result.DetectionResult.Response,
		})
		run.GetTelemetryTracer().StartSpan(run, "agent_message", map[string]interface{}{
			"type":    tracing.SpanEventResponse,
			"message": result.DetectionResult.Response,
		}, tracing.SpanTypeEvent, nil)
		messageID := uuid.New().String()
		run.State.Conversation.AddAgentMessage(&iris.Message{
			ID:      messageID,
			From:    responseactor.MessageResponse,
			Content: result.DetectionResult.Response,
		})
	})

	mentions := lo.FlatMap(messages, func(m *iris.Message, _ int) []iris.Mention {
		return m.Mentions
	})
	// extract urls and mentions, and prepare data in workspace
	// this must happen after message event
	wg.Go(func() {
		// preprocess 会发送 step 展示给用户，必须在 intent detection 回复消息之后
		<-detect
		if result.DetectionResult.Intercepted {
			return
		}
		preprocessors := map[iris.MentionType]queryrecognizer.MentionPreprocessor{
			iris.MentionTypeCodebase:   &queryrecognizer.CodebaseMentionPreprocessor{},
			iris.MentionTypeLarkDoc:    &queryrecognizer.LarkDocMentionPreprocessor{},
			iris.MentionTypeAeolus:     &queryrecognizer.AeolusMentionPreprocessor{},
			iris.MentionTypeAttachment: &queryrecognizer.AttachmentMentionPreprocessor{},
		}

		preprocessorWg := conc.NewWaitGroup()
		start := time.Now()

		if len(lo.Filter(mentions, func(m iris.Mention, _ int) bool {
			return preprocessors[m.GetType()] != nil
		})) == 0 {
			preprocessorWg.Wait()
			return
		}
		hasVisiblePreprocessor := len(lo.Filter(mentions, func(m iris.Mention, _ int) bool {
			return preprocessors[m.GetType()] != nil && preprocessors[m.GetType()].Visible()
		})) != 0
		// 只有包含 visible preprocessor 才需要展示，否则不需要
		_, preprocessStep := a.createPreprocessStep(run, hasVisiblePreprocessor)
		if hasVisiblePreprocessor {
			defer func() {
				publisher.ReportPlanStepUpdate(iris.EventPlanStepUpdated{
					ID:     preprocessStep.StepID,
					PlanID: PreprocessorID,
					StepID: preprocessStep.StepID,
					Status: "completed",
				})
			}()
		}
		mentionsByType := lo.GroupBy(mentions, func(m iris.Mention) string {
			return string(m.GetType())
		})

		for _, mentions := range mentionsByType {
			if len(mentions) == 0 {
				continue
			}
			preprocessor, ok := preprocessors[mentions[0].GetType()]
			if !ok {
				continue
			}
			preprocessorWg.Go(func() {
				step := run.CreateStep(&iris.CreateStepOption{
					ExecutorAgent: PreprocessorID,
					Parent:        preprocessStep,
				})
				step.Status = iris.AgentRunStepStatusRunning
				step.Action = actions.ToTool(fmt.Sprintf("mcp:prepare_%s", string(mentions[0].GetType())), string(mentions[0].GetType()), func(c *iris.AgentRunContext, input string) (map[string]any, error) {
					return nil, nil
				})
				step.Inputs = map[string]any{
					"input": lo.Uniq(lo.Map(mentions, func(m iris.Mention, _ int) string {
						return m.DisplayString()
					})),
				}
				step.Outputs = map[string]any{
					"status": "downloading...",
				}
				publisher.ReportStep(step)
				if preprocessor.Visible() {
					publisher.ReportToolCall(step, iris.ToolCallStatusStarted, "")
					publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, "") // frontend only display completed status
				}
				metrics.AR.QueryPreprocessorThroughput.WithTags(&metrics.QueryPreprocessorTag{
					Type: string(mentions[0].GetType()),
				}).Add(float64(len(mentions)))
				err := preprocessor.Preprocess(run, mentions)
				if err != nil {
					logger.Errorf("failed to preprocess lark doc mentions: %v", err)
				}

				// extra step for codebase mention, load project memory and display it on frontend
				if mentions[0].GetType() == iris.MentionTypeCodebase {
					processedRepos := make(map[string]bool)

					for _, m := range mentions {
						var mention *iris.CodebaseMention
						if mention, ok = m.(*iris.CodebaseMention); !ok {
							continue
						}

						// 如果已经处理过这个仓库，跳过
						if processedRepos[mention.RepoName] {
							continue
						}

						ws := workspace.GetWorkspace(run)
						repo := ws.GetRepositoryByRepoName(mention.RepoName)
						if repo == nil {
							continue
						}
						if repo.Memory == nil || len(repo.Memory.FilePaths) == 0 {
							continue
						}

						// 标记已处理
						processedRepos[mention.RepoName] = true

						projectMemoryStep := run.CreateStep(&iris.CreateStepOption{
							ExecutorAgent: PreprocessorID,
							Parent:        preprocessStep,
						})
						projectMemoryStep.Status = iris.AgentRunStepStatusRunning
						projectMemoryStep.Action = actions.ToTool(fmt.Sprintf("mcp:prepare_%s_memory", string(mentions[0].GetType())), string(mentions[0].GetType()), func(c *iris.AgentRunContext, input string) (map[string]any, error) {
							return nil, nil
						})
						projectMemoryStep.Inputs = map[string]any{
							"input": "加载仓库记忆文件:" + m.DisplayString(),
						}

						projectMemoryStep.Outputs = map[string]any{
							"status": "loading...",
							"memory": repo.Memory.FilePaths,
						}

						publisher.ReportStep(projectMemoryStep)
						publisher.ReportToolCall(projectMemoryStep, iris.ToolCallStatusStarted, "")
						publisher.ReportToolCall(projectMemoryStep, iris.ToolCallStatusCompleted, "")

						projectMemoryStep.Status = iris.AgentRunStepStatusSuccess
						publisher.ReportStep(projectMemoryStep)
					}

				}

				step.Status = iris.AgentRunStepStatusSuccess
				if preprocessor.Visible() {
					publisher.ReportStep(step)
				}
			})
		}
		err := preprocessorWg.WaitAndRecover().AsError()
		if err != nil {
			logger.Errorf("failed to preprocess mentions: %v", err)
		}
		result.PreprocessedItems = lo.FlatMap(lo.Values(preprocessors), func(preprocessor queryrecognizer.MentionPreprocessor, _ int) []queryrecognizer.PreprocessedMentionItem {
			return preprocessor.GetPreprocessedItems(run)
		})
		totalCost := lo.SumBy(result.PreprocessedItems, func(item queryrecognizer.PreprocessedMentionItem) int64 {
			return item.Cost
		})
		metrics.AR.QueryPreprocessorTimeSaved.WithTags(&metrics.QueryPreprocessorTag{
			Type: "total",
		}).Observe(float64(totalCost - time.Since(start).Milliseconds()))
	})

	// do speculative knowledge retrieval for faster planning
	wg.Go(func() {
		annotationWG := conc.NewWaitGroup()
		annotationWG.Go(func() {
			// detect scenario, after all content processor
			scenarioPreprocessor := queryrecognizer.ScenarioPreprocessor{}
			err := scenarioPreprocessor.Preprocess(run, mentions)
			if err != nil {
				logger.Errorf("failed to preprocess scenario: %v", err)
			}
		})
		annotationWG.Go(func() {
			artifactsAnnotator := queryrecognizer.ArtifactsPreprocessor{}
			err := artifactsAnnotator.Preprocess(run, mentions)
			if err != nil {
				logger.Errorf("failed to preprocess artifacts: %v", err)
			}
		})
		err = annotationWG.WaitAndRecover().AsError()
		if err != nil {
			logger.Errorf("failed to annotate query: %v", err)
		}

		expetedArtifacts := iris.RetrieveStoreByKey[queryrecognizer.ArtifactsAnnotationStore](run, queryrecognizer.ArtifactsAnnotationStoreKey).ExpectedArtifacts
		agent := "dynamic_planner"
		variant := run.GetConfig().GetVariantByScene("planner")
		wg := conc.NewWaitGroup()
		wg.Go(func() {
			_, _ = knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     historyRequirements + lastRequirements + expetedArtifacts,
				Knowledge: a.knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategorySystem,
				Param: knowledges.RetrieveParam{
					Agent:   agent,
					Variant: variant,
				},
				Cache: a.kgCache,
			})
		})
		wg.Go(func() {
			_, _ = knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     historyRequirements + lastRequirements + expetedArtifacts,
				Knowledge: a.knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategoryTool,
				Param: knowledges.RetrieveParam{
					Agent:   agent,
					Variant: variant,
				},
				Cache: a.kgCache,
			})
		})
		wg.Go(func() {
			scenario := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)
			_, _ = knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     historyRequirements + lastRequirements + expetedArtifacts,
				Knowledge: a.knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategoryScenario,
				Param: knowledges.RetrieveParam{
					Agent:    agent,
					Variant:  variant,
					Scenario: scenario,
				},
				Cache: a.kgCache,
			})
		})
		err = wg.WaitAndRecover().AsError()
		if err != nil {
			logger.Errorf("failed to retrieve tool knowledge: %v", err)
		}
	})

	// wait for all async tasks to complete
	err = wg.WaitAndRecover().AsError()
	if err != nil {
		logger.Errorf("failed to preprocess query: %v", err)
	}
	result.EnhancedRequirements = enhancedRequirements

	// if the run is cancelled, return the error
	return result, run.Err()
}

func (a *PlanActAgent) createPreprocessStep(run *iris.AgentRunContext, visible bool) (planStep *iris.AgentRunStep, preprocessStep *iris.AgentRunStep) {
	publisher := run.GetPublisher()
	planStep = run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: a.Name(),
	})
	preprocessStep = run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: PreprocessorID,
		Parent:        planStep,
	})
	publisher.ReportStep(planStep)
	publisher.ReportStep(preprocessStep)
	if visible {
		publisher.ReportPlanUpdateData(iris.EventPlanUpdated{
			ID:     planStep.StepID,
			Status: "created",
			PlanSteps: []iris.PlanStep{
				{
					ID:          preprocessStep.StepID,
					Actor:       PreprocessorID,
					Status:      "created",
					Description: "准备环境...", // TODO: i18n
				},
			},
		})
		publisher.ReportPlanStepUpdate(iris.EventPlanStepUpdated{
			ID:          preprocessStep.StepID,
			StepID:      preprocessStep.StepID,
			Description: "准备环境...",
			Status:      "running",
		})
	}
	return planStep, preprocessStep
}

func (a *PlanActAgent) storeContext(ctx context.Context, run *iris.AgentRunContext) {
	if a.Name() != AgentIDV1_5 && a.Name() != AgentIDV1 {
		return
	}
	if run.CtxStorage == nil {
		return
	}
	// 保存上下文到存储中， 移除 goroutine 防止并发操作 map 导致的 panic
	logger := run.GetLogger()
	nerr := run.CtxStorage.Store(ctx, iris.StorageTypeAgentRunContext, iris.StoreOption{AgentRunContext: run})
	if nerr != nil {
		logger.Errorf("failed to store run context: %v", nerr)
	}
	nerr = run.CtxStorage.Store(ctx, iris.StorageTypeAgentRunContextStore, iris.StoreOption{AgentRunContextStore: run.State.Store})
	if nerr != nil {
		logger.Errorf("failed to store run context store: %v", nerr)
	}
}

func (a *PlanActAgent) loadPlanner(run *iris.AgentRunContext) GeneralPlanner {
	switch a.Name() {
	case AgentIDV1_5:
		exp, err := a.getExperience(run)
		progressPlan := ""
		if err != nil {
			run.GetLogger().Errorf("failed to get experience: %v", err)
		} else if exp != nil && exp.SOP == nil {
			// 如果有 SOP 则不使用 Progress Plan
			progressPlan = lo.FromPtr(exp.ProgressPlan)
		}
		var serialIdentifier, progreActIdentifier string
		if restart, _ := run.CtxStorage.IsRestart(); restart {
			ctx := context.Background()
			mem := memory.GetAgentMemory(run)
			previousSerial, ok := lo.Find(mem.ActionMemory, func(item *memory.ActionMemoryItem) bool {
				return strings.HasPrefix(item.ExecutorAgent, "dynamic_planner-serial")
			})
			if ok && previousSerial != nil {
				serialIdentifier = previousSerial.ExecutorAgent
			}
			agentRunContextStore, exist, err := run.CtxStorage.Recover(ctx, iris.StorageTypeAgentRunContextStore)
			if err != nil {
				run.GetLogger().Errorf("failed to recover context store: %v", err)
			} else if exist && agentRunContextStore != nil {
				store, ok := agentRunContextStore.(map[string]json.RawMessage)
				if ok {
					for k, _ := range store {
						if strings.HasPrefix(k, "dynamic_planner-dynamic_planner-ProgreAct") {
							progreActIdentifierTemp := strings.TrimPrefix(k, "dynamic_planner-")
							progreActIdentifier = strings.TrimSuffix(progreActIdentifierTemp, "-ProgreAct")
						}
					}
				}
			}
			run.GetLogger().Infof("find progre and serial identifier: %s, %s", progreActIdentifier, serialIdentifier)
		}
		return NewDynamicPlanner(DynamicPlannerCreateOption{
			MaxSteps:               20,
			Variant:                run.GetConfig().GetVariantByScene("planner_think"),
			LLMHooks:               getLLMHooks(run.GetConfig().GetVariantByScene("planner_think"), isEnabledBytedTools(run)),
			ThinkCallbacks:         []agents.ThinkCallback{agents.NewThinkCallback(run.GetLogger(), run, "planner")},
			Knowledgebase:          a.knowledgebase,
			KGCache:                a.kgCache,
			ExperienceProgressPlan: progressPlan,
			SerialIdentifier:       serialIdentifier,
			ProgreActIdentifier:    progreActIdentifier,
		})
	default:
		return &Planner{
			AgentInfo: iris.AgentInfo{
				Identifier: "planner",
				Desc:       "a general planner",
			},
			LLMHooks:      getLLMHooks(run.GetConfig().GetVariantByScene("planner_think"), isEnabledBytedTools(run)),
			Knowledgebase: a.knowledgebase,
		}
	}
}

func (a *PlanActAgent) getNextStep(run *iris.AgentRunContext, logger iris.Logger) (step *iris.AgentRunStep, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			"plan",
			agentrace.WithObjectSpanData(
				map[string]any{
					"available_actors": iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey).ActorsDesc,
				},
			),
		)
	run = run.WithContext(ctx)
	defer func() {
		if step != nil && step.Thought != nil {
			span.UpdateData(agentrace.SpanData[map[string]any]{
				Data: map[string]any{
					"plan": map[string]any{
						"rationale":  step.Thought.Rationale,
						"agent":      step.Thought.Tool,
						"parameters": step.Thought.Parameters,
						"progress":   step.Thought.Data["progress"],
					},
				},
			})
		}
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()

	tSpan, _ := run.GetTelemetryTracer().StartSpan(run, "plan", map[string]interface{}{
		"type":   tracing.SpanEventPlan,
		"inputs": iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey).Requirements,
	}, tracing.SpanTypeSpan, nil)
	defer func() {
		if err != nil {
			tSpan.SetError(err)
		}
		if step != nil && step.Thought != nil {
			tSpan.SetTag("outputs", step.Thought.Data["progress"])
		}
		tSpan.End()
	}()

	step, err = a.prepareExperience(run)
	mem := memory.GetAgentMemory(run)
	if step == nil {
		logger.Infof("not prepared experience: %v", err)
	} else {
		mem.AddAction((*memory.ActionMemoryItem)(step))

		step.Status = iris.AgentRunStepStatusRunning
		run.GetPublisher().ReportStep(step)

		// Add a store to indicate the template exp sop is enabled.
		iris.UpdateStoreByKey(run, genexp.TemplateExpSOPStoreKey, genexp.TemplateExpSOPStore{
			Enabled: true,
		})

		return step, nil
	}

	run.State.CurrentStep = nil
	step, err = a.planner.NextStep(run, run)
	if step == nil && run.State.CurrentStep != nil {
		logger.Warnf("agent should return created step %s, but not returned", run.State.CurrentStep.StepID)
		step = run.State.CurrentStep
	}

	if step != nil {
		switch {
		case err != nil && iris.IsRecoverable(err):
			step.Status = iris.AgentRunStepStatusError
		case err != nil:
			step.Status = iris.AgentRunStepStatusFailed
		default:
			step.Status = iris.AgentRunStepStatusRunning
		}
		step.Error = err

		mem.AddAction((*memory.ActionMemoryItem)(step))

		run.GetPublisher().ReportStep(step)
	}

	if iris.IsCompleted(err) {
		return step, err
	}

	// Currently all errors are recoverable.
	return step, iris.NewRecoverable(err)
}

func InitializeBackgroundServices(run *iris.AgentRunContext) {
	logger := run.GetLogger()
	logger.Infof("initializing background services...")
	_, err := mcpserver.StartService(run)
	if err != nil {
		logger.Errorf("failed to start mcp service: %v", err)
	}
	var mcpConfig []*mcptool.MCPProvider
	if err = mapstructure.Decode(run.Parameters["mcps"], &mcpConfig); err != nil {
		logger.Warnf("failed to decode mcp config: %v", err)
	}
	_, enableInternalSearch := lo.Find(mcpConfig, func(item *mcptool.MCPProvider) bool {
		return item.ID == websearch.InternalSearchMCPID
	})
	mcpConfig = lo.Filter(mcpConfig, func(item *mcptool.MCPProvider, _ int) bool {
		return item.ID != websearch.InternalSearchMCPID
	})
	run.Parameters[websearch.EnableInternalSearchParameterKey] = enableInternalSearch
	// 初始化指定的 MCP providers
	if len(mcpConfig) > 0 {
		providers := []*mcptool.MCPProvider{}
		for _, mcpProvider := range mcpConfig {
			// 分类型处理, aime官方的
			if mcpProvider.Type == mcptool.MCPSourceAIME {
				// 判断是不是在默认的list里面
				if mcptool.IsBuiltInProvider(mcpProvider.ID) {
					providers = append(providers, mcptool.ProviderDict[mcpProvider.ID])
					if mcpProvider.ID == mcptool.Argos.ID {
						// hard code一下，选了argos把mcptool.ArgosNew放进去，等后面和argos合并了再改掉 by lizhenan
						providers = append(providers, mcptool.ArgosNew)
					}
				} else {
					providers = append(providers, mcpProvider)
					logger.Warnf("invalid mcp id : %s", mcpProvider.ID)
				}
			} else if mcpProvider.ID == mcptool.VisActorChartAssistant.ID {
				continue
			} else {
				// 自定义以及字节云之类第三方的mcp
				providers = append(providers, mcpProvider)
			}
		}
		mcptool.ProviderRegistry.Providers = providers
		// PPT MCP默认注入，因为不想给用户看到所以不配置在服务端
		// mcptool.ProviderRegistry.Providers = append(mcptool.ProviderRegistry.Providers, mcptool.PPT)
		// Cosy 默认注入， 因为不想给用户看到所以不配置在服务端
		if runtime.GOARCH == "amd64" && !lo.ContainsBy(mcptool.ProviderRegistry.Providers, func(provider *mcptool.MCPProvider) bool { return provider.ID == mcptool.Cosy.ID }) {

			// 项目空间场景下，暂时不使用cosy工具
			isProjectSpace := agspentity.CheckParamIsProjectSpace(run.Parameters[agspentity.RuntimeParametersSpaceInfomation])
			logger.Infof("[InitializeBackgroundServices]-[project_space_logic]: project_space_scenario check if register cosy, isProjectSpace:%+v", isProjectSpace)
			if !isProjectSpace {
				mcptool.ProviderRegistry.Providers = append(mcptool.ProviderRegistry.Providers, mcptool.Cosy)
			}
		}
		if iris.CurrentRegion() != iris.RegionI18n {
			mcptool.ProviderRegistry.Providers = append(mcptool.ProviderRegistry.Providers, mcptool.BitsWorkFlow)
		}
	} else {
		// 兼容上线的旧版本，空的情况下，默认加载所有的mcp
		providers := []*mcptool.MCPProvider{
			mcptool.AMap,
			mcptool.GoogleMaps,
			mcptool.Figma,
			mcptool.Arxiv,
			mcptool.Unsplash,
			mcptool.GoogleImageSearch,
			mcptool.YFinance,
			mcptool.Meego,
			mcptool.Lark,
			mcptool.BitsAnalysis,
			mcptool.Codebase,
			mcptool.Argos,
			mcptool.OnCall,
			mcptool.BitsWorkFlow,
		}
		// cosy mcp currently only support amd64
		if runtime.GOARCH == "amd64" {
			providers = append(providers, mcptool.Cosy)
		}
		mcptool.ProviderRegistry.Providers = providers
	}
	// try to start browser anyway
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("failed to start browser mcp service: %v", r)
			}
		}()
		_, err = browser.StartMCPService(run)
		if err != nil {
			logger.Errorf("failed to start browser mcp service: %v", err)
		}
	}()
}

func (a *PlanActAgent) getLastRequirements(run *iris.AgentRunContext, messages []*iris.Message) string {
	return strings.Join(lo.Map(messages, func(msg *iris.Message, _ int) string {
		return msg.Content
	}), "\n")
}

func (a *PlanActAgent) getLastStep(run *iris.AgentRunContext) entity.PlanStep {
	mem := memory.GetAgentMemory(run)
	plannerSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
		return step.ExecutorAgent == a.planner.Name()
	})
	lastStep := util.Last(plannerSteps)
	var result entity.Plan
	if lastStep != nil {
		mapstructure.Decode(lastStep.Outputs, &result)
	}
	lastActor := util.Last(result.Steps)
	return lastActor
}

func (a *PlanActAgent) updatePlannerRequirements(run *iris.AgentRunContext, messages []*iris.Message) {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	requirements := a.getLastRequirements(run, messages)
	if store.InitialRequirements == "" {
		store.InitialRequirements = requirements
		store.Requirements = requirements
		iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
	} else {
		lastActor := a.getLastStep(run)
		run.GetLogger().Infof("last actor name: %s, last actor evaluation: %s", lastActor.ActorName, lastActor.Evaluation)
		store.Requirements += requirements
		manualStep := a.createAskUserStep(run, store.Requirements, requirements,
			lo.Ternary(lastActor.ActorName == reporteractor.Identifier && lastActor.Evaluation == controltool.ConclusionEvaluationSuccess, "finished", "interrupted by user"))
		a.SaveManualStep(run, manualStep, store) // this will update the store
	}
	run.State.Conversation.CleanDirtyMessage(lo.Map(messages, func(msg *iris.Message, _ int) string {
		return msg.ID
	}))
}

func (a *PlanActAgent) updateStore(run *iris.AgentRunContext, preprocessQueryResult PreprocessQueryResult) {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	store.RelatedKnowledge = preprocessQueryResult.RelatedKnowledge
	store.RelatedWorkspaceKnowledges = preprocessQueryResult.RelatedWorkspaceKnowledges
	store.EnhancedRequirements = preprocessQueryResult.EnhancedRequirements
	if preprocessQueryResult.ExperiencePrompt != "" {
		store.ExperiencesHistory = preprocessQueryResult.ExperiencePrompt
	}
	iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
}

func (a *PlanActAgent) createAskUserStep(run *iris.AgentRunContext, oldReq, requirements string, status string) *iris.AgentRunStep {
	run.GetLogger().Infof("append ask user step, status: %s, planner name: %s", status, a.planner.Name())
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: a.planner.Name(),
	})
	assistantContent := &bytes.Buffer{}
	assistantTemplate := prompts.GetUserInterruptTemplate(run.GetConfig().GetVariantByScene("planner"))
	_ = assistantTemplate.Execute(assistantContent, map[string]any{
		"Status":       status,
		"Requirements": oldReq,
	})
	step.Thought = &iris.Thought{
		Content: assistantContent.String(),
	}
	var result entity.Plan
	store := iris.RetrieveStoreByKey[queryrecognizer.ArtifactsAnnotationStore](run, queryrecognizer.ArtifactsAnnotationStoreKey)
	result.Steps = append(result.Steps, entity.PlanStep{
		ActorName:  "ask_user",
		Evaluation: controltool.ConclusionEvaluationSuccess,
		Input:      "What else can I help you with?",
		Output:     requirements + store.ExpectedArtifacts,
	})

	step.Outputs = agentutil.ValueToMap(result)
	return step
}

func (a *PlanActAgent) SaveManualStep(run *iris.AgentRunContext, step *iris.AgentRunStep, store entity.PlannerStore) {
	run.GetLogger().Infof("save manual step: %+v", step)

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	store.CurRound++
	iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
	a.planner.IncreaseCurrentRound(run)
}

func (a *PlanActAgent) uploadProjectArtifacts(run *iris.AgentRunContext, projectReference iris.ProjectReference) []iris.Attachment {
	if len(projectReference) == 0 {
		return []iris.Attachment{}
	}
	projectArtifactVersions, err := projectartifact.GetProjectArtifactManager(run).ProcessProjectArtifacts(run, projectReference, projectartifact.UploadAttachmentsOptions...)
	if err != nil {
		// 部分项目产物处理出错，仅打印日志，部分成功的项目产物继续上传
		run.GetLogger().Errorf("failed to process project artifacts: %v", err)
	}
	return lo.Map(projectArtifactVersions, func(projectArtifactVersion projectartifact.ProjectArtifactVersion, _ int) iris.Attachment {
		return iris.Attachment{
			ArtifactID: projectArtifactVersion.Artifact.ArtifactID,
			Name:       projectArtifactVersion.Name,
			Path:       projectArtifactVersion.Path,
			Type:       iris.AttachmentTypeProject,
			URL:        "",
			Version:    projectArtifactVersion.Version,
		}
	})
}

func enhanceRequriement(run *iris.AgentRunContext, req string) string {
	variant := run.GetConfig().GetVariantByScene("enhance_user_requirement")
	if !lo.Contains([]string{agententity.VariantNewbie}, variant) {
		return req
	}
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "enhance_req",
	})
	templateset := prompts.GetEnhanceReqTemplate(variant)
	messages, err := prompt.ComposeVaryMessages(
		[]prompt.ComposeVaryMessageOption{
			prompt.WithSystemMessage(templateset.SystemTmpl, map[string]any{
				"UserLanguage": agents.GetUserLanguage(run.Parameters),
			}),
			prompt.WithUserMessage(templateset.UserTmpl, map[string]any{
				"Content": req,
			}),
		},
	)
	if err != nil {
		run.GetLogger().Errorf("failed to compose enhance user requirements prompt: %v", err)
		return req
	}
	content, _ := agents.Think(run, "enhance_req", messages,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)

	// 解析返回的内容，提取 Enhanced Content 部分并移除 Rationale
	enhancedContent := extractEnhancedContent(content.Content)

	// 直接拼接原始需求和增强内容
	finalRequirements := req + "\n" + enhancedContent

	step.Conclusion["conclusion"] = finalRequirements
	step.Finish(step.Conclusion, nil)
	run.GetLogger().Debugf("!!!!!!!!!!!!!!!!!!!!!enhanced requirements: %s", finalRequirements)
	return finalRequirements
}

// extractEnhancedContent 从模型返回的内容中提取 Enhanced Content 部分，移除 Rationale
func extractEnhancedContent(content string) string {
	enhancedContentMarker := "## Enhanced Content"
	importantNotesMarker := "Important Notes:"
	idx := strings.Index(content, enhancedContentMarker)
	if idx == -1 {
		return ""
	}
	// ## Enhanced Content 下 需要判断是否有 Important Notes:
	// 如果没有，直接返回空字符串
	notesIdx := strings.Index(content, importantNotesMarker)
	if notesIdx == -1 {
		return ""
	}

	// 提取 Enhanced Content 部分（跳过标记本身）
	enhancedSection := content[idx+len(enhancedContentMarker):]
	// 移除开头的换行符和空白字符
	enhancedSection = strings.TrimLeft(enhancedSection, "\n\r \t")

	return enhancedSection
}

func validateScenesConfig(run *iris.AgentRunContext) {
	logger := run.GetLogger()
	config := run.GetConfig()
	scenes, ok := agententity.AllScenes["general_agent"]
	if !ok {
		logger.Errorf("failed to list all scenes for general agent")
		return
	}
	modelScenes := []string{}
	for _, modelConfig := range config.ModelScenesConfig.SceneSpecifications {
		modelScenes = append(modelScenes, modelConfig.Scenes...)
	}
	for _, scene := range scenes {
		if !lo.Contains(modelScenes, scene.Name) {
			logger.Warnf("scene %s is not configured for model, description: %s", scene.Name, scene.Description)
		}
	}
	// variantsScenes := []string{}
	// for _, varConfig := range config.AgentVariantConfig.Variants {
	// 	variantsScenes = append(variantsScenes, varConfig.Scenes...)
	// }
	// for _, scene := range scenes {
	// 	if !lo.Contains(variantsScenes, scene.Name) {
	// 		logger.Warnf("scene %s is not configured for variants, description: %s", scene.Name, scene.Description)
	// 	}
	// }
}

const (
	AgentMessageCanceledChinese = "当前任务已被用户手动取消。"
	AgentMessageCanceledEnglish = "The current task has been canceled by the user."
)

func sendCanceledMessageToUser(run *iris.AgentRunContext) {
	messages := run.State.Conversation.GetMessages()
	publisher := run.GetPublisher()
	replyTo := ""
	lastUserMessage, _, ok := lo.FindLastIndexOf(messages, func(msg *iris.Message) bool {
		return msg.From == iris.MessageFromUser
	})
	if ok {
		replyTo = lastUserMessage.ID
	}
	messageID := uuid.New().String()
	message := lo.Ternary(agents.GetUserLanguage(run.Parameters) == "English", AgentMessageCanceledEnglish, AgentMessageCanceledChinese)
	publisher.ReportMessage(iris.EventAgentMessage{
		ID:      messageID,
		ReplyTo: replyTo,
		Content: message,
	})
	run.GetTelemetryTracer().StartSpan(run, "agent_message", map[string]interface{}{
		"type":    tracing.SpanEventResponse,
		"message": message,
	}, tracing.SpanTypeEvent, nil)
	run.State.Conversation.AddAgentMessage(&iris.Message{
		ID:      messageID,
		From:    responseactor.MessageResponse,
		Content: message,
	})
}
