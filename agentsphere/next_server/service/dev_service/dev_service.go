package dev_service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/port/bits"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_ai_graph/kitex_gen/bits/ai/graph"
	"code.byted.org/overpass/bits_ai_graph/rpc/bits_ai_graph"
	"github.com/pkg/errors"
	"go.uber.org/fx"
)

type Service struct {
	dao        *dal.DAO
	bitsClient bits.Client
}

type CreateServiceOption struct {
	fx.In

	DAO        *dal.DAO
	BitsClient bits.Client
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		dao:        opt.DAO,
		bitsClient: opt.BitsClient,
	}

	return s, nil
}

func (s *Service) SearchDevService(ctx context.Context, req *nextagent.SearchServiceRequest, username, jwt string) ([]*nextagent.Service, int64, error) {
	var uploadedDevServices []*po.NextDevServicePO
	var err error

	uploadedDevServices, _, err = s.dao.ListDevService(ctx, dal.ListDevServiceOption{
		SpaceID: req.GetSpaceID(),
		Type:    req.GetServiceType().String(),
	})
	if err != nil {
		return nil, 0, errors.WithMessage(err, "[SearchService] failed to list dev service from db")
	}

	uploadedDevServiceM := gslice.ToMap(uploadedDevServices, func(item *po.NextDevServicePO) (string, struct{}) {
		return item.BizID, struct{}{}
	})

	if len(req.GetQuery()) == 0 {
		resp, err := s.bitsClient.SearchRecentDeployment(ctx, &bits.SearchRecentDeploymentRequest{
			Username: username,
			Page:     int(req.GetPageNum() - 1),
			PageSize: int(req.GetPageSize()),
		})
		if err != nil {
			return nil, 0, errors.WithMessage(err, "[SearchService] failed to search recent deployment")
		}
		if resp.Data == nil {
			return []*nextagent.Service{}, 0, nil
		}

		return gslice.Map(resp.Data.Deployments, func(item *bits.Deployment) *nextagent.Service {
			uniqueID := genTceServiceUniqueID(item.Psm)
			_, isUploaded := uploadedDevServiceM[uniqueID]

			return &nextagent.Service{
				UniqueId:     uniqueID,
				Type:         nextagent.ServiceType_SERVICE_TYPE_TCE,
				Name:         item.Psm,
				ControlPlane: nextagent.ControlPlane_CONTROL_PLANE_CN,
				IsUploaded:   isUploaded,
				Url:          genTceServiceUrl(item.Psm),
			}
		}), int64(resp.Data.Total), nil
	}

	resp, err := s.bitsClient.SearchTCEComponent(ctx, &bits.SearchComponentRequest{
		Keyword:       req.GetQuery(),
		Limit:         int(req.GetPageSize()),
		ProjectType:   bits.ProjectTypeTCE,
		ControlPlanes: []int{bits.ControlPlaneCN},
	}, jwt)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "[SearchService] failed to search tce component")
	}

	return gslice.FilterMap(resp.Projects, func(item *bits.Project) (*nextagent.Service, bool) {
		if item.ProjectType != bits.ProjectTypeTCE {
			return nil, false
		}

		uniqueID := genTceServiceUniqueID(item.ProjectUniqueId)
		_, isUploaded := uploadedDevServiceM[uniqueID]

		return &nextagent.Service{
			UniqueId:     uniqueID,
			Type:         nextagent.ServiceType_SERVICE_TYPE_TCE,
			Name:         item.ProjectName,
			ControlPlane: nextagent.ControlPlane_CONTROL_PLANE_CN,
			IsUploaded:   isUploaded,
			Url:          genTceServiceUrl(item.ProjectName),
		}, true
	}), int64(len(resp.Projects)), nil
}

func (s *Service) CreateDevService(ctx context.Context, devServices []*nextagent.Service, spaceID, username string) error {
	existServices, _, err := s.dao.ListDevService(ctx, dal.ListDevServiceOption{
		SpaceID: spaceID,
		Type:    nextagent.ServiceType_SERVICE_TYPE_TCE.String(),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list dev services")
	}
	existM := gslice.ToMap(existServices, func(s *po.NextDevServicePO) (string, struct{}) {
		return s.BizID, struct{}{}
	})

	err = s.dao.CreateDevServices(ctx, gslice.FilterMap(devServices, func(s *nextagent.Service) (*po.NextDevServicePO, bool) {
		if _, ok := existM[s.GetUniqueId()]; ok {
			return nil, false
		}

		return &po.NextDevServicePO{
			SpaceID:      spaceID,
			BizID:        s.GetUniqueId(),
			Name:         s.GetName(),
			Type:         s.GetType().String(),
			Creator:      username,
			ControlPlane: s.GetControlPlane().String(),
		}, true
	}))
	if err != nil {
		return errors.WithMessage(err, "failed to create dev services")
	}

	return nil
}

func (s *Service) ListDevService(ctx context.Context, req *nextagent.ListServiceRequest) ([]*nextagent.Service, int64, error) {
	devServices, _, err := s.dao.ListDevService(ctx, dal.ListDevServiceOption{
		SpaceID: req.GetSpaceID(),
		Type:    nextagent.ServiceType_SERVICE_TYPE_TCE.String(),
	})
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list dev service from db")
	}

	if req.GetQuery() != "" {
		devServices = gslice.Filter(devServices, func(item *po.NextDevServicePO) bool {
			return strings.Contains(strings.ToLower(item.Name), strings.ToLower(req.GetQuery()))
		})
	}

	if len(req.GetCreators()) > 0 {
		devServices = gslice.Filter(devServices, func(item *po.NextDevServicePO) bool {
			return gslice.Contains(req.GetCreators(), item.Creator)
		})
	}
	devServiceStatusM := GetDevServiceStatusM(ctx, devServices, req.GetSpaceID())
	if len(req.GetProcessStatus()) > 0 {
		devServices = gslice.Filter(devServices, func(item *po.NextDevServicePO) bool {
			return gslice.Contains(req.GetProcessStatus(), devServiceStatusM[item.BizID])
		})
	}

	total := len(devServices)
	start := operatorx.IfThen(req.GetPageNum() > 0, int((req.GetPageNum()-1)*req.GetPageSize()), 0)
	end := operatorx.IfThen(req.GetPageSize() > 0, start+int(req.GetPageSize()), total)
	if start >= total {
		return []*nextagent.Service{}, 0, nil
	}
	if end > total {
		end = total
	}

	return gslice.Map(devServices[start:end], func(item *po.NextDevServicePO) *nextagent.Service {
		return &nextagent.Service{
			UniqueId:      item.BizID,
			Type:          nextagent.ServiceType_SERVICE_TYPE_TCE,
			Name:          item.Name,
			ControlPlane:  nextagent.ControlPlane_CONTROL_PLANE_CN,
			IsUploaded:    true,
			ProcessStatus: devServiceStatusM[item.BizID],
			Url:           genTceServiceUrl(item.Name),
		}
	}), int64(total), nil
}

func GetDevServiceStatusM(ctx context.Context, devServices []*po.NextDevServicePO, spaceID string) map[string]string {
	graphResp, err := bits_ai_graph.RawCall.ListResourceStatus(ctx, &graph.ListResourceStatusRequest{
		AimeSpaceId: spaceID,
		Scene:       graph.SceneType_TCEService,
	}, callopt.WithRPCTimeout(5*time.Second), callopt.WithVRegion(env.VREGION_CHINANORTH))
	if err != nil {
		log.V1.CtxError(ctx, "list resource status failed: %v", err)
	}
	log.V1.CtxInfo(ctx, "list resource status: %+v", graphResp.GetResources())

	tceServiceResource := gslice.Filter(graphResp.GetResources(), func(item *graph.ResourceWithStatus) bool {
		return item.GetResourceType() == graph.ResourceType_TceService
	})

	tceServiceStatusM := gslice.ToMap(tceServiceResource, func(item *graph.ResourceWithStatus) (string, string) {
		return genTceServiceUniqueID(item.GetTceService().GetPsm()), item.GetStatus()
	})

	for _, s := range devServices {
		if _, ok := tceServiceStatusM[s.BizID]; !ok {
			if time.Since(s.UpdatedAt) > 2*time.Hour {
				tceServiceStatusM[s.BizID] = nextagent.ProcessStatusFailed
				continue
			}

			tceServiceStatusM[s.BizID] = nextagent.ProcessStatusProcessing
		}
	}

	return tceServiceStatusM
}

func (s *Service) DeleteDevService(ctx context.Context, req *nextagent.DeleteServiceRequest) error {
	err := s.dao.DeleteDevServices(ctx, req.GetSpaceID(), gslice.Map(req.GetServices(), func(item *nextagent.Service) string {
		return item.GetUniqueId()
	}))
	if err != nil {
		return errors.WithMessage(err, "failed to delete dev service")
	}

	return nil
}

func genTceServiceUniqueID(psm string) string {
	return fmt.Sprintf("%s-tce-cn", psm)
}

func genTceServiceUrl(psm string) string {
	return fmt.Sprintf("https://bits.bytedance.net/app_center/detail/tce/%s", psm)
}
