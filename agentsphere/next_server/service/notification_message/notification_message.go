package notification_message

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gslice"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/uuid"
)

type Service struct {
	idGen       uuid.Generator
	dao         *dal.DAO
	larkService *larkservice.Service
}

type CreateServiceOption struct {
	fx.In

	DAO         *dal.DAO
	LarkService *larkservice.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		dao:         opt.DAO,
		idGen:       uuid.GetDefaultGenerator(nil),
		larkService: opt.LarkService,
	}

	return s, nil
}

func (s *Service) CreateNotificationMessage(ctx context.Context, req *nextagent.CreateNotificationMessageRequest, username string) (string, error) {
	messageID := s.idGen.NewID()
	err := s.dao.CreateNotificationMessage(ctx, &po.NextNotificationMessagePO{
		Uid:      messageID,
		Title:    req.GetTitle(),
		Content:  req.GetContent(),
		Link:     req.GetLinkInfo().GetLink(),
		LinkName: req.GetLinkInfo().GetName(),
		Creator:  username,
		IsTop:    lo.ToPtr(req.GetIsTop()),
		Type:     req.GetType(),
	})
	if err != nil {
		return "", errors.WithMessage(err, "[CreateNotificationMessage] failed to create message")
	}

	for _, rc := range req.GetReceiveConfig() {
		switch rc.GetReceiveType() {
		case nextagent.ReceiveTypeAll:
			err = s.dao.BatchCreateReceiverNotificationMessage(ctx, []*po.NextReceiverNotificationMessagePO{
				{
					MessageID:   messageID,
					ReceiveType: rc.GetReceiveType(),
					Status:      string(entity.MessageStatusUnread),
				},
			})
			if err != nil {
				return "", errors.WithMessage(err, "[CreateNotificationMessage] failed to BatchCreateReceiverNotificationMessage")
			}
		case nextagent.ReceiveTypeDepartment, nextagent.ReceiveTypePersonal:
			err = s.dao.BatchCreateReceiverNotificationMessage(ctx, gslice.Map(rc.GetReceivers(), func(item string) *po.NextReceiverNotificationMessagePO {
				return &po.NextReceiverNotificationMessagePO{
					MessageID:   messageID,
					Receiver:    lo.ToPtr(item),
					ReceiveType: rc.GetReceiveType(),
					Status:      string(entity.MessageStatusUnread),
				}
			}))
			if err != nil {
				return "", errors.WithMessage(err, "[CreateNotificationMessage] failed to create message")
			}

			if rc.GetReceiveType() == nextagent.ReceiveTypePersonal && req.GetSendLark() {
				for _, receiver := range rc.GetReceivers() {
					user := receiver

					go func(receiver string) {
						err = s.larkService.SendNotificationMessage(ctx, receiver, req.GetLinkInfo().GetLink(), req.GetTitle(), req.GetContent(), req.GetLinkInfo().GetName())
						if err != nil {
							log.V1.CtxWarn(ctx, "failed to send lark. err: %s", err)
						}
					}(user)
				}
			}
		default:
		}
	}

	return messageID, nil
}

func (s *Service) ListNotificationMessage(ctx context.Context, username, department string) ([]*nextagent.NotificationMessage, error) {
	groupMessages := make([]*po.NextReceiverNotificationMessagePO, 0)
	allNotificationMessages, err := s.dao.ListReceiverNotificationMessage(ctx, dal.ListReceiverNotificationMessage{
		ReceiveType: entity.ReceiveTypeAll,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "[ListNotificationMessage] failed to list all message")
	}
	groupMessages = append(groupMessages, allNotificationMessages...)

	departmentMessages, err := s.dao.ListReceiverNotificationMessage(ctx, dal.ListReceiverNotificationMessage{
		Department:  department,
		ReceiveType: entity.ReceiveTypeDepartment,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "[ListNotificationMessage] failed to list department message")
	}
	groupMessages = append(groupMessages, departmentMessages...)

	if len(groupMessages) > 0 {
		readRecords, err := s.dao.ListReadNotificationMessage(ctx, dal.ListReadNotificationMessage{
			Username: username,
			MessageIDs: gslice.FilterMap(groupMessages, func(item *po.NextReceiverNotificationMessagePO) (string, bool) {
				return item.MessageID, item.Status == string(entity.MessageStatusUnread)
			}),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "[ListNotificationMessage] failed to list read message")
		}

		if len(readRecords) > 0 {
			readRecordM := gslice.ToMap(readRecords, func(item *po.NextReadNotificationMessagePO) (string, struct{}) {
				return item.MessageID, struct{}{}
			})

			for _, m := range groupMessages {
				if _, ok := readRecordM[m.MessageID]; ok {
					m.Status = string(entity.MessageStatusRead)
				}
			}
		}
	}

	personalMessages, err := s.dao.ListReceiverNotificationMessage(ctx, dal.ListReceiverNotificationMessage{
		Username:    username,
		ReceiveType: entity.ReceiveTypePersonal,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "[ListNotificationMessage] failed to list personal message")
	}

	filteredPersonalMessages := gslice.Filter(personalMessages, func(item *po.NextReceiverNotificationMessagePO) bool {
		return item.Status != string(entity.MessageStatusRecalled)
	})
	// 优先展示个人消息
	var mergedMessages []*po.NextReceiverNotificationMessagePO
	personalMessagesM := gslice.ToMap(filteredPersonalMessages, func(item *po.NextReceiverNotificationMessagePO) (string, *po.NextReceiverNotificationMessagePO) {
		return item.MessageID, item
	})
	mergedMessages = append(mergedMessages, filteredPersonalMessages...)
	for _, m := range groupMessages {
		if _, ok := personalMessagesM[m.MessageID]; !ok {
			if m.Status == string(entity.MessageStatusRecalled) {
				continue
			}

			mergedMessages = append(mergedMessages, m)
		}
	}

	messageIDs := gslice.Map(mergedMessages, func(item *po.NextReceiverNotificationMessagePO) string {
		return item.MessageID
	})
	messages, err := s.dao.ListNotificationMessage(ctx, gslice.Uniq(messageIDs))
	if err != nil {
		return nil, errors.WithMessage(err, "[ListNotificationMessage] failed to list notification message")
	}
	messageM := gslice.ToMap(messages, func(item *po.NextNotificationMessagePO) (string, *po.NextNotificationMessagePO) {
		return item.Uid, item
	})

	res := make([]*nextagent.NotificationMessage, 0)
	for _, m := range mergedMessages {
		messageDetail := messageM[m.MessageID]
		if messageDetail == nil {
			continue
		}

		res = append(res, &nextagent.NotificationMessage{
			MessageID: m.MessageID,
			Title:     messageDetail.Title,
			Content:   messageDetail.Content,
			LinkInfo: &nextagent.LinkInfo{
				Link: messageDetail.Link,
				Name: messageDetail.LinkName,
			},
			Status:    m.Status,
			IsTop:     lo.FromPtr(messageDetail.IsTop),
			CreatedAt: m.CreatedAt.Unix(),
			Creator:   messageDetail.Creator,
			Type:      messageDetail.Type,
		})
	}

	// 排序
	// 未读在前，已读在后
	// 置顶在前，非置顶在后
	// 时间倒序
	gslice.SortBy(res, func(m1, m2 *nextagent.NotificationMessage) bool {
		if m1.Status == nextagent.MessageStatusRead &&
			m2.Status == nextagent.MessageStatusUnread {
			return false
		}

		if m1.Status == nextagent.MessageStatusUnread &&
			m2.Status == nextagent.MessageStatusRead {
			return true
		}

		if m1.IsTop == true && m2.IsTop == false {
			return true
		}

		if m1.IsTop == false && m2.IsTop == true {
			return false
		}

		return m1.CreatedAt > m2.CreatedAt
	})

	return res, nil
}

func (s *Service) UpdateNotificationMessageStatus(ctx context.Context, req *nextagent.UpdateNotificationMessageStatusRequest, username string) error {
	switch req.GetStatus() {
	case nextagent.MessageStatusRecalled:
		err := s.dao.UpdateReceiverNotificationMessageStatus(ctx, req.GetMessageID(), req.GetStatus())
		if err != nil {
			return errors.WithMessage(err, "[UpdateNotificationMessageStatus] failed to update receiver message")
		}
	case nextagent.MessageStatusRead:
		receiverMessages, err := s.dao.ListReceiverNotificationMessage(ctx, dal.ListReceiverNotificationMessage{
			MessageIDs: []string{req.GetMessageID()},
		})
		if err != nil {
			return errors.WithMessage(err, "[ListReceiverNotificationMessage] failed to list receiver message")
		}

		if len(receiverMessages) == 0 ||
			receiverMessages[0].Status == nextagent.MessageStatusRead {
			return nil
		}
		// 更新 all 或者 department 消息状态
		// 在已读消息关联表插入记录
		// 查询一下是否有已读记录
		switch entity.ReceiveType(receiverMessages[0].ReceiveType) {
		case entity.ReceiveTypeAll, entity.ReceiveTypeDepartment:
			readRecords, err := s.dao.ListReadNotificationMessage(ctx, dal.ListReadNotificationMessage{
				Username:   username,
				MessageIDs: []string{req.GetMessageID()},
			})
			if err != nil {
				return errors.WithMessage(err, "[UpdateNotificationMessageStatus] failed to list read message")
			}

			if len(readRecords) > 0 {
				return nil
			}

			err = s.dao.CreateReadNotificationMessage(ctx, dal.CreateReadNotificationMessageOpts{
				Username:  username,
				MessageID: req.GetMessageID(),
			})
			if err != nil {
				return errors.WithMessage(err, "[UpdateNotificationMessageStatus] failed to create read message")
			}
		case entity.ReceiveTypePersonal:
			err = s.dao.UpdateReceiverNotificationMessageStatus(ctx, req.GetMessageID(), req.GetStatus())
			if err != nil {
				return errors.WithMessage(err, "[UpdateNotificationMessageStatus] failed to update receiver message")
			}

			// 不阻塞
			err = s.dao.CreateReadNotificationMessage(ctx, dal.CreateReadNotificationMessageOpts{
				Username:  username,
				MessageID: req.GetMessageID(),
			})
			if err != nil {
				log.V1.CtxError(ctx, "[CreateReadNotificationMessage] failed to create read message: %v", err)
			}
		default:
		}
	default:
	}

	return nil
}
