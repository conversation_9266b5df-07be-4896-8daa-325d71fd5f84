package knowledgebase

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/idgenerator"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/es"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/port/tos"
	"code.byted.org/devgpt/kiwis/port/tqs"
	"code.byted.org/devgpt/kiwis/port/viking"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"github.com/hashicorp/go-multierror"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"
	poolsdk "github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
	"golang.org/x/sync/errgroup"
)

type Service struct {
	idGen       uuid.Generator
	dao         *dal.DAO
	esCli       es.Client
	esIndex     string
	vikingCli   *viking.Client
	larkCli     lark.Client
	larkService *larkservice.Service
	loader      DocumentLoader
	tosCli      tos.Client
	idIntGen    idgenerator.IDGenerator
	// recallers make it easy to test.
	recallers                []recallerFunc
	knowledgebaseMQ          rocketmq.Client
	redis                    redis.Client
	permissionService        *permission.Service
	nextAgentKnowledgeConfig *libtcc.GenericConfig[config.NextAgentKnowledgebaseConfig]
	embeddingClient          *hertz.Client
	tqsCli                   tqs.Client
	patrolTable              string
	pruneTable               string
}

type CreateServiceOption struct {
	fx.In

	DAO                      *dal.DAO
	ESCli                    es.Client
	LarkCli                  lark.Client
	LarkService              *larkservice.Service
	TosCli                   tos.Client
	IdIntGen                 idgenerator.IDGenerator
	VikingClient             *viking.Client
	NextAgentKnowledgeConfig *libtcc.GenericConfig[config.NextAgentKnowledgebaseConfig]
	Conf                     *config.AgentSphereConfig
	KnowledgebaseMQ          rocketmq.Client `name:"knowledgebase_mq"`
	Redis                    redis.Client
	PermissionService        *permission.Service
	TqsCli                   tqs.Client
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		dao:                      opt.DAO,
		esCli:                    opt.ESCli,
		esIndex:                  opt.Conf.KnowledgebaseConfig.ElasticSearchRecallIndex,
		idGen:                    uuid.GetDefaultGenerator(nil),
		larkCli:                  opt.LarkCli,
		larkService:              opt.LarkService,
		loader:                   NewLarkLoader(opt.LarkCli),
		tosCli:                   opt.TosCli,
		idIntGen:                 opt.IdIntGen,
		vikingCli:                opt.VikingClient,
		nextAgentKnowledgeConfig: opt.NextAgentKnowledgeConfig,
		knowledgebaseMQ:          opt.KnowledgebaseMQ,
		redis:                    opt.Redis,
		permissionService:        opt.PermissionService,
		tqsCli:                   opt.TqsCli,
		patrolTable:              opt.Conf.KnowledgebaseConfig.PatrolSegmentTable,
		pruneTable:               opt.Conf.KnowledgebaseConfig.PruneSegmentTable,
	}
	embeddingClient, err := hertz.NewClient("https://bitsai.bytedance.net", hertz.NewHTTPClientOption{
		Timeout: time.Second * 30,
	})
	if err != nil {
		return nil, err
	}
	s.embeddingClient = embeddingClient
	s.recallers = []recallerFunc{
		s.recallSegmentsFromViking,
		s.recallSegmentsFromES,
	}
	return s, nil
}

func (s *Service) GetDatasetBySpaceID(ctx context.Context, spaceID string) (*entity.Dataset, error) {
	return s.dao.GetDatasetBySpaceID(ctx, spaceID)
}

func (s *Service) MGetDatasetBySpaceIDs(ctx context.Context, spaceIDs []string) (map[string]*entity.Dataset, error) {
	return s.dao.MGetDatasetBySpaceIDs(ctx, spaceIDs)
}

// CreateDataset creates a new dataset with a unique ID.
func (s *Service) CreateDataset(ctx context.Context, spaceID, owner string) (*entity.Dataset, error) {
	id := s.idGen.NewID()
	dataset, err := s.dao.CreateDataset(ctx, dal.DatasetCreateOption{ID: id, SpaceID: spaceID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create dataset")
	}
	space, err := s.dao.GetSpace(ctx, spaceID, true)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space")
	}
	var isSpacePublic *bool
	if space.Type.IsProject() { // 项目空间的知识库默认项目内公开
		isSpacePublic = lo.ToPtr(true)
	}
	_, err = s.permissionService.CreateResource(ctx, permission.CreateResourceOption{
		Owner:         owner,
		Type:          entity.ResourceTypeKnowledgebase,
		ExternalID:    id,
		Status:        lo.ToPtr(entity.ResourceStatusPrivate),
		GroupID:       &spaceID,
		IsSpacePublic: isSpacePublic,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create resource for dataset")
	}
	return dataset, nil
}

// SearchDocsFromLark searches for documents in Lark (not aime knowledgebase) based on the provided query.
// It can be used to recommend documents from Lark based on the user's query.
func (s *Service) SearchDocsFromLark(ctx context.Context, username string, query string, datasetID string, importType entity.ImportType) ([]*entity.LarkDocument, *entity.WikiSpace, error) {
	query = strings.TrimSpace(query)
	sourceType, fileKey := devaiutil.ParseLarkDocURL(ctx, query)
	if importType == entity.ImportTypeWikiTree && sourceType != "wiki" {
		logs.CtxWarn(ctx, "[SearchDocsFromLark] invalid query: %s", query)
		return []*entity.LarkDocument{}, nil, nil
	}

	accessToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get lark user")
	}

	if len(sourceType) != 0 {
		_, isSupported := entity.GetDocumentSourceType(sourceType)
		if !isSupported {
			return nil, nil, fmt.Errorf("invalid document sourceType: %s", sourceType)
		}

		switch importType {
		case entity.ImportTypeWikiTree:
			return s.SearchDocumentsFromWikiTree(ctx, SearchDocumentsFromWikiTreeOpts{
				Query:       query,
				FileKey:     fileKey,
				AccessToken: accessToken,
				DatasetID:   datasetID,
			})
		default:
			documents, err := s.SearchDocumentsFromSingle(ctx, SearchDocumentsFromSingleOpts{
				Query:       query,
				FileKey:     fileKey,
				SourceType:  sourceType,
				AccessToken: accessToken,
				DatasetID:   datasetID,
			})
			if err == nil && len(documents) > 0 {
				return documents, nil, nil
			}
		}
	}
	var (
		wg                   sync.WaitGroup
		searchLarkObjectResp *lark.SearchLarkObjectRespData
		searchLarkWikiResp   *lark.SearchWikiNodeData
		docTokens            []string
		wikiNodeTokens       []string
	)
	pool, _ := poolsdk.NewPool(2)
	defer pool.Release()
	wg.Add(1)
	_ = pool.Submit(func() {
		defer wg.Done()
		searchLarkObjectResp, err = s.larkCli.SearchLarkObject(ctx, &lark.SearchLarkObjectRequest{
			SearchKey: query,
			Count:     20,
			Offset:    0,
			DocsTypes: []string{"doc"},
		}, accessToken)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to search lark object, err: %v", err)
		}
	})

	wg.Add(1)
	_ = pool.Submit(func() {
		defer wg.Done()
		searchLarkWikiResp, err = s.larkCli.SearchWikiNode(ctx, &lark.SearchWikiNodeRequest{
			Query:    query,
			PageSize: lo.ToPtr(20),
		}, accessToken)
		if err != nil {
			logs.V1.CtxError(ctx, "[searchByTitle] failed to search wiki node: %v", err)
		}
	})
	wg.Wait()
	if searchLarkObjectResp != nil {
		docTokens = make([]string, 0, len(searchLarkObjectResp.DocsEntities))
		for _, docsEntity := range searchLarkObjectResp.DocsEntities {
			docTokens = append(docTokens, docsEntity.DocsType+"/"+docsEntity.DocsToken)
		}
	}
	if searchLarkWikiResp != nil {
		wikiNodeTokens = make([]string, 0, len(searchLarkWikiResp.Items))
		for _, node := range searchLarkWikiResp.Items {
			wikiNodeTokens = append(wikiNodeTokens, node.NodeID)
		}
	}
	docs, err := s.larkCli.SearchLarkData(ctx, 10, fmt.Sprintf("帮我找些 title 和 '%s' 有关的信息", query), accessToken, &lark.PassageDisableSearch{HelpdeskDisable: true, DocTokens: docTokens, WikiNodeTokens: wikiNodeTokens})
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to search lark data")
	}
	fileKeyMap := make(map[string]bool)
	ret := make([]*entity.LarkDocument, 0, len(docs.Data.Passages))
	for _, doc := range docs.Data.Passages {
		extra := lark.SearchPassageExtra{}
		err = json.Unmarshal([]byte(doc.Extra), &extra)
		if err != nil {
			return nil, nil, errors.WithMessage(err, "failed to unmarshal extra")
		}
		contentType := entity.DocumentContentType(extra.GetContentType())
		if !contentType.IsSupport() {
			logs.Error("not support document type: %s, url:%s", contentType, doc.URL)
			continue
		}
		_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		if _, ok := fileKeyMap[fileKey]; ok { // 文档去重
			continue
		}
		fileKeyMap[fileKey] = true
		doc.Content = postContent(doc.Content)
		ret = append(ret, &entity.LarkDocument{
			Title:       doc.Title,
			URL:         doc.URL,
			Content:     doc.Content,
			ContentType: string(contentType),
			OwnerName:   extra.OwnerName,
		})
	}

	existDocs, err := s.dao.ListDocumentsByDatasetIDSourceUids(ctx, datasetID, gslice.Map(ret, func(sp *entity.LarkDocument) string {
		_, sourceUid := devaiutil.ParseLarkDocURL(ctx, sp.URL)

		return sourceUid
	}))
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to list documents")
	}
	existDocMap := gslice.ToMap(existDocs, func(d *entity.Document) (string, struct{}) {
		return d.SourceUid, struct{}{}
	})
	for _, doc := range ret {
		_, sourceUid := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		_, doc.IsUploaded = existDocMap[sourceUid]
	}

	return ret, nil, nil
}

type SearchDocumentsFromWikiTreeOpts struct {
	Query       string
	FileKey     string
	AccessToken string
	DatasetID   string
}

func (s *Service) SearchDocumentsFromWikiTree(ctx context.Context, opts SearchDocumentsFromWikiTreeOpts) ([]*entity.LarkDocument, *entity.WikiSpace, error) {
	wikiNode, err := s.larkCli.GetWikiNodeInfo(ctx, opts.FileKey, opts.AccessToken)
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to get wiki node: %s", opts.FileKey)
	}

	if wikiNode == nil || wikiNode.Data == nil ||
		wikiNode.Data.Node == nil || wikiNode.Data.Node.SpaceId == nil {
		return nil, nil, errors.WithMessagef(err, "invalid wiki node: %s", opts.FileKey)
	}

	rootNode := wikiNode.Data.Node
	spaceID := *rootNode.SpaceId
	var spaceName string
	spaceName, err = s.redis.Get(ctx, genWikiSpaceKey(spaceID))
	if err != nil {
		logs.CtxInfo(ctx, "[SearchDocsFromLark] no wiki space name in cache")

		wikiSpace, err := s.larkCli.GetWikiSpaceInfo(ctx, spaceID, opts.AccessToken)
		if err != nil {
			logs.CtxWarn(ctx, "[SearchDocsFromLark] failed to get wiki space: %v", err)
		}
		if wikiSpace == nil || wikiSpace.Data == nil ||
			wikiSpace.Data.Space == nil {
			spaceName = *wikiNode.Data.Node.Title
		} else {
			spaceName = *wikiSpace.Data.Space.Name
			s.redis.SetNX(ctx, genWikiSpaceKey(spaceID), spaceName, time.Minute*30)
		}
	}

	res := make([]*entity.LarkDocument, 0)
	sourceUID2OwnerIDM := make(map[string]string)
	sourceUID2OwnerIDM[opts.FileKey] = *rootNode.Owner

	res = append(res, &entity.LarkDocument{
		Title:       *rootNode.Title,
		URL:         opts.Query,
		ContentType: *rootNode.ObjType,
		IsRoot:      true,
		HasChild:    *rootNode.HasChild,
		Meta: &entity.DocumentMeta{
			Title:       *rootNode.Title,
			SourceUid:   opts.FileKey,
			ContentType: entity.DocumentContentType(*rootNode.ObjType),
			SourceType:  entity.DocumentSourceTypeLarkWiki,
			DocToken:    *rootNode.ObjToken,
			CreateTime:  ToTime(*rootNode.ObjCreateTime),
			UpdateTime:  ToTime(*rootNode.ObjEditTime),
		},
	})

	if *rootNode.HasChild {
		documents, ownerIDM, err := s.getLarkDocumentsFromWiki(ctx, spaceID, opts.FileKey, opts.AccessToken)
		if err != nil {
			return nil, nil, errors.WithMessagef(err, "failed to get wiki documents")
		}

		res = append(res, documents...)
		sourceUID2OwnerIDM = gmap.Union(sourceUID2OwnerIDM, ownerIDM)
	}

	sourceUID2NameM, err := s.getLarkUserName(ctx, sourceUID2OwnerIDM)
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to get wiki owner name")
	}

	existDocs, err := s.dao.ListDocumentsByDatasetIDSourceUids(ctx, opts.DatasetID, gslice.Map(res, func(d *entity.LarkDocument) string {
		_, fileKey := devaiutil.ParseLarkDocURL(ctx, d.URL)

		return fileKey
	}))
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to list documents")
	}
	existDocMap := gslice.ToMap(existDocs, func(d *entity.Document) (string, struct{}) {
		return genLarkWikiUrl(d.SourceUid), struct{}{}
	})

	for _, doc := range res {
		_, doc.IsUploaded = existDocMap[doc.URL]
		if doc.Meta == nil {
			continue
		}
		doc.OwnerName = sourceUID2NameM[doc.Meta.SourceUid]
		doc.Meta.Owner = doc.OwnerName
	}

	return res, &entity.WikiSpace{
		ID:   spaceID,
		Name: spaceName,
	}, nil
}

func (s *Service) getLarkDocumentsFromWiki(ctx context.Context, spaceID, parentNodeToken, accessToken string) ([]*entity.LarkDocument, map[string]string, error) {
	nodes, err := s.larkCli.ListWikiNodesV2(ctx, spaceID, parentNodeToken, accessToken)
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to list wiki nodes")
	}

	sourceUID2OwnerIDM := make(map[string]string)
	res := gslice.FilterMap(nodes, func(n *larkwiki.Node) (*entity.LarkDocument, bool) {
		if n == nil {
			return nil, false
		}

		if *n.NodeType != "origin" {
			return nil, false
		}

		if _, isSupported := entity.GetDocumentSourceType(*n.ObjType); !isSupported {
			return nil, false
		}

		sourceUID2OwnerIDM[*n.NodeToken] = *n.Owner
		return &entity.LarkDocument{
			Title:       *n.Title,
			URL:         genLarkWikiUrl(*n.NodeToken),
			ContentType: *n.ObjType,
			IsRoot:      false,
			HasChild:    *n.HasChild,
			Meta: &entity.DocumentMeta{
				Title:       *n.Title,
				SourceUid:   *n.NodeToken,
				ContentType: entity.DocumentContentType(*n.ObjType),
				SourceType:  entity.DocumentSourceTypeLarkWiki,
				DocToken:    *n.ObjToken,
				CreateTime:  ToTime(*n.ObjCreateTime),
				UpdateTime:  ToTime(*n.ObjEditTime),
			},
		}, true
	})

	return res, sourceUID2OwnerIDM, nil
}

func (s *Service) ImportWiki(ctx context.Context, event *entity.ImportWikiEvent) error {
	var (
		err         error
		accessToken string
		task        *po.NextKnowledgeTaskPO
	)

	defer func() {
		if err != nil && task != nil {
			err = s.dao.UpdateKnowledgeTask(ctx, dal.UpdateKnowledgeTaskOption{
				UID:       event.TaskID,
				Status:    string(entity.KnowledgeTaskStatusFailed),
				FailedCnt: task.FailedCount + 1,
			})
			if err != nil {
				logs.CtxError(ctx, "[ImportWiki]failed to update knowledge task status, taskID: %s, failed cnt:%d", event.TaskID, task.FailedCount+1)
			}
		}

		err = s.dao.UpdateKnowledgeTask(ctx, dal.UpdateKnowledgeTaskOption{
			UID:    event.TaskID,
			Status: string(entity.KnowledgeTaskStatusSucceeded),
		})
		if err != nil {
			logs.CtxError(ctx, "[ImportWiki]failed to update knowledge task status, taskID: %s", event.TaskID)
		}
	}()

	task, err = s.dao.GetKnowledgeTask(ctx, event.TaskID)
	if err != nil {
		return errors.WithMessage(err, "[ImportWiki]failed to get knowledge task")
	}

	err = s.dao.UpdateKnowledgeTask(ctx, dal.UpdateKnowledgeTaskOption{
		UID:    event.TaskID,
		Status: string(entity.KnowledgeTaskStatusRunning),
	})
	if err != nil {
		return errors.WithMessage(err, "[ImportWiki]failed to update knowledge task status")
	}

	accessToken, err = s.larkService.GetUserAccessToken(ctx, event.Creator)
	if err != nil {
		return errors.WithMessage(err, "[ImportWiki]failed to get lark user")
	}

	err = s.processSingleWiki(ctx, event.WikiDocUrl, accessToken, event.DatasetID, event.Creator, event.SpaceName)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) processSingleWiki(ctx context.Context, docUrl, accessToken, datasetID, username, spaceName string) error {
	_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(docUrl))
	docs, _, err := s.SearchDocumentsFromWikiTree(ctx, SearchDocumentsFromWikiTreeOpts{
		Query:       docUrl,
		FileKey:     fileKey,
		AccessToken: accessToken,
		DatasetID:   datasetID,
	})
	if err != nil {
		return err
	}

	documents := gslice.FilterMap(docs, func(d *entity.LarkDocument) (UploadDocumentOption, bool) {
		if d == nil || d.IsUploaded {
			return UploadDocumentOption{}, false
		}

		_, fileKey = devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(d.URL))
		return UploadDocumentOption{
			SourceType:    entity.DocumentSourceTypeLarkWiki,
			SourceUid:     fileKey,
			ImportType:    entity.ImportTypeWikiTree,
			WikiSpaceName: spaceName,
			Meta:          d.Meta,
		}, true
	})

	_, err = s.UploadDocuments(ctx, datasetID, username, documents)
	if err != nil {
		return err
	}

	for _, doc := range docs {
		if !doc.HasChild || doc.IsRoot {
			continue
		}

		err = s.processSingleWiki(ctx, doc.URL, accessToken, datasetID, username, spaceName)
		if err != nil {
			return err
		}
	}

	return nil
}

type SearchDocumentsFromSingleOpts struct {
	Query       string
	FileKey     string
	SourceType  string
	AccessToken string
	DatasetID   string
}

func (s *Service) SearchDocumentsFromSingle(ctx context.Context, opts SearchDocumentsFromSingleOpts) ([]*entity.LarkDocument, error) {
	fileMeta, err := s.larkCli.GetFilesMeta(ctx, []*lark.RequestDoc{
		{
			DocToken: opts.FileKey,
			DocType:  opts.SourceType,
		},
	}, opts.AccessToken, lark.Option{UserIDType: lark.UserIDTypeOpenID})
	if err == nil && len(fileMeta.Metas) > 0 {
		docType := entity.DocumentContentType(lo.FromPtr(fileMeta.Metas[0].DocType))
		if !docType.IsSupport() {
			return nil, errors.WithMessagef(err, "invalid document type: %s", docType)
		}
		users, err := s.larkCli.ListLarkUsers(ctx, []string{lo.FromPtr(fileMeta.Metas[0].OwnerId)}, "")
		if err != nil {
			logs.V1.CtxError(ctx, "failed to list lark users, err: %v", err)
		}
		var ownerName string
		if len(users) > 0 {
			r := strings.Split(lo.FromPtr(users[0].Email), "@")
			if len(r) != 0 {
				ownerName = r[0]
			}
		}
		existDocs, err := s.dao.ListDocumentsByDatasetIDSourceUids(ctx, opts.DatasetID, []string{opts.FileKey})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to list wiki documents")
		}

		return []*entity.LarkDocument{
			{
				Title:       lo.FromPtr(fileMeta.Metas[0].Title),
				URL:         opts.Query, //url与请求一致
				IsUploaded:  len(existDocs) > 0,
				ContentType: lo.FromPtr(fileMeta.Metas[0].DocType),
				OwnerName:   ownerName,
			},
		}, nil
	}

	return nil, err
}

func (s *Service) getLarkUserName(ctx context.Context, sourceUID2OwnerIDM map[string]string) (map[string]string, error) {
	var (
		res          = make(map[string]string)
		userID2NameM = make(map[string]string)
	)
	users, err := s.larkCli.ListLarkUsers(ctx, gslice.Uniq(gmap.Values(sourceUID2OwnerIDM)), "")
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		r := strings.Split(lo.FromPtr(user.Email), "@")
		if len(r) == 0 || r[0] == "" {
			continue
		}

		userID2NameM[lo.FromPtr(user.OpenId)] = r[0]
	}

	for sourceUID, ownerID := range sourceUID2OwnerIDM {
		res[sourceUID] = userID2NameM[ownerID]
	}

	return res, nil
}

type UploadDocumentOption struct {
	SourceType    entity.DocumentSourceType
	SourceUid     string
	ImportType    entity.ImportType
	WikiSpaceName string
	Meta          *entity.DocumentMeta
}

const (
	uploadPoolSize = 5
)

func (s *Service) UploadDocuments(ctx context.Context, datasetID, username string, opts []UploadDocumentOption) ([]*entity.Document, error) {
	if len(opts) == 0 {
		return []*entity.Document{}, nil
	}

	dataset, _ := s.dao.GetDataset(ctx, datasetID)
	if dataset == nil {
		return nil, errors.New("dataset not found")
	}
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return nil, lark.ErrLarkAuthFailed
		}
		return nil, err
	}
	// 过滤掉空间下已经存在的文档
	existDocs, _ := s.dao.ListDocumentsByDatasetIDSourceUids(ctx, datasetID, lo.Map(opts, func(opt UploadDocumentOption, index int) string {
		return opt.SourceUid
	}))
	docMap := make(map[string]bool)
	for _, doc := range existDocs {
		docMap[doc.SourceUid] = true
	}
	pool, err := poolsdk.NewPool(uploadPoolSize)
	if err != nil {
		logs.V1.CtxError(ctx, "[AsyncBatchUpdateContent] failed to create pool: %v", err)
		return nil, err
	}
	defer pool.Release()
	var lock sync.Mutex
	docs := make([]*entity.Document, 0, len(opts))
	wg := &sync.WaitGroup{}
	for _, opt := range opts {
		if docMap[opt.SourceUid] { // 已经存在的文档跳过
			logs.V1.CtxInfo(ctx, "source uid: %s already exists", opt.SourceUid)
			continue
		}

		// 尽量减少飞书调用次数，优先使用上游获取到的元数据
		if opt.Meta != nil {
			docs = append(docs, &entity.Document{
				ID:                s.idGen.NewID(),
				DatasetID:         datasetID,
				Creator:           username,
				Title:             opt.Meta.Title,
				SourceType:        opt.SourceType,
				SourceUid:         opt.SourceUid,
				ContentType:       opt.Meta.ContentType,
				ProcessStatus:     entity.DocumentProcessStatusProcessing,
				LastUpdatedAt:     opt.Meta.UpdateTime,
				Owner:             opt.Meta.Owner,
				DocToken:          lo.ToPtr(opt.Meta.DocToken),
				DocumentCreatedAt: opt.Meta.CreateTime,
				ImportType:        opt.ImportType,
				WikiSpaceName:     opt.WikiSpaceName,
			})

			continue
		}

		opt := opt
		wg.Add(1)
		_ = pool.Submit(func() {
			defer wg.Done()
			meta, loadErr := s.loader.LoadMeta(ctx, username, userToken, MetaOption{
				SourceType: opt.SourceType,
				SourceUid:  opt.SourceUid,
			})
			if loadErr != nil {
				err = loadErr
				return
			}
			doc := &entity.Document{
				ID:                s.idGen.NewID(),
				DatasetID:         datasetID,
				Creator:           username,
				Title:             meta.Title,
				SourceType:        meta.SourceType,
				SourceUid:         meta.SourceUid,
				ContentType:       meta.ContentType,
				ProcessStatus:     entity.DocumentProcessStatusProcessing,
				LastUpdatedAt:     meta.UpdateTime,
				Owner:             meta.Owner,
				DocToken:          lo.ToPtr(meta.DocToken),
				DocumentCreatedAt: meta.CreateTime,
				ImportType:        opt.ImportType,
				WikiSpaceName:     opt.WikiSpaceName,
			}
			lock.Lock()
			docs = append(docs, doc)
			lock.Unlock()
		})
	}
	wg.Wait()
	if err != nil {
		return nil, err
	}
	if len(docs) == 0 {
		return nil, nil
	}
	err = s.saveDocumentsToES(ctx, docs)
	if err != nil {
		return nil, err
	}
	ret, err := s.dao.BatchCreateDocument(ctx, docs)
	if err != nil {
		return nil, err
	}
	err = s.SendBatchUpsertContentMessage(ctx, docs, false)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send batch upsert content: %v", err)
	}
	return ret, nil
}

func (s *Service) DeleteDocument(ctx context.Context, datasetID, documentID string) error {
	err := s.dao.DeleteDocument(ctx, datasetID, documentID)
	if err != nil {
		return err
	}
	err = s.deleteDocumentToES(ctx, documentID)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}
	return s.DeleteDocumentContent(ctx, datasetID, documentID)
}

func (s *Service) BatchDeleteDocument(ctx context.Context, datasetID string, documentIDs []string) error {
	if len(documentIDs) == 0 {
		return nil
	}

	err := s.dao.BatchDeleteDocument(ctx, datasetID, documentIDs)
	if err != nil {
		return err
	}
	err = s.batchDeleteDocumentToES(ctx, documentIDs)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}

	var errs = &multierror.Error{}
	eg := &errgroup.Group{}
	eg.SetLimit(5)
	lock := &sync.Mutex{}

	for _, did := range documentIDs {
		documentID := did
		eg.Go(func() error {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "[BatchDeleteDocument] panic: %v", err)
				}
			}()

			err = s.DeleteDocumentContent(ctx, datasetID, documentID)
			if err != nil {
				lock.Lock()
				errs = multierror.Append(errs, err)
				lock.Unlock()
			}

			return nil
		})
	}

	eg.Wait()
	return errs.ErrorOrNil()
}

func (s *Service) UpdateDocument(ctx context.Context, datasetID, documentID, username string) error {
	doc, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return err
	}
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return lark.ErrLarkAuthFailed
		}
		return err
	}
	meta, err := s.loader.LoadMeta(ctx, username, userToken, MetaOption{
		SourceType: doc.SourceType,
		SourceUid:  doc.SourceUid,
	})
	if err != nil {
		return err
	}

	err = s.dao.UpdateDocument(ctx, documentID, &dal.UpdateDocumentOption{
		ProcessStatus:     lo.ToPtr(entity.DocumentProcessStatusProcessing),
		Title:             lo.ToPtr(meta.Title),
		Owner:             lo.ToPtr(meta.Owner),
		LastUpdatedAt:     lo.ToPtr(meta.UpdateTime),
		DocumentCreatedAt: lo.ToPtr(meta.CreateTime),
	})
	if err != nil {
		return err
	}
	doc.Title = meta.Title
	doc.Owner = meta.Owner
	doc.LastUpdatedAt = meta.UpdateTime
	doc.ContentType = meta.ContentType
	doc.DocToken = lo.ToPtr(meta.DocToken)
	doc.ProcessStatus = entity.DocumentProcessStatusProcessing
	err = s.updateDocumentToES(ctx, &updateDocumentToESOption{
		DocumentID:        documentID,
		Title:             lo.ToPtr(meta.Title),
		Owner:             lo.ToPtr(meta.Owner),
		LastUpdatedAt:     lo.ToPtr(meta.UpdateTime),
		DocumentCreatedAt: lo.ToPtr(meta.CreateTime),
		ProcessStatus:     lo.ToPtr(string(entity.DocumentProcessStatusProcessing)),
	})
	if err != nil {
		return err
	}
	err = s.SendBatchUpsertContentMessage(ctx, []*entity.Document{doc}, false)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send batch upsert content: %v", err)
	}
	return nil
}

func (s *Service) BatchUpdateDocument(ctx context.Context, datasetID, username string, documentIDs []string) error {
	if len(documentIDs) == 0 {
		return nil
	}

	docsM, err := s.dao.MGetDocument(ctx, documentIDs)
	if err != nil {
		return err
	}
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return lark.ErrLarkAuthFailed
		}
		return err
	}

	var opts []*updateDocumentToESOption
	var updatedDocs []*entity.Document
	eg := errgroup.Group{}
	eg.SetLimit(5)
	lock := sync.Mutex{}

	for _, d := range docsM {
		doc := d

		eg.Go(func() error {
			meta, err := s.loader.LoadMeta(ctx, username, userToken, MetaOption{
				SourceType: doc.SourceType,
				SourceUid:  doc.SourceUid,
			})
			if err != nil {
				return err
			}

			err = s.dao.UpdateDocument(ctx, doc.ID, &dal.UpdateDocumentOption{
				ProcessStatus: lo.ToPtr(entity.DocumentProcessStatusProcessing),
				Title:         lo.ToPtr(meta.Title),
				Owner:         lo.ToPtr(meta.Owner),
				LastUpdatedAt: lo.ToPtr(meta.UpdateTime),
			})
			if err != nil {
				return err
			}
			doc.Title = meta.Title
			doc.Owner = meta.Owner
			doc.LastUpdatedAt = meta.UpdateTime
			doc.ContentType = meta.ContentType
			doc.DocToken = lo.ToPtr(meta.DocToken)
			doc.ProcessStatus = entity.DocumentProcessStatusProcessing
			lock.Lock()
			updatedDocs = append(updatedDocs, doc)
			opts = append(opts, &updateDocumentToESOption{
				DocumentID:    doc.ID,
				Title:         lo.ToPtr(meta.Title),
				Owner:         lo.ToPtr(meta.Owner),
				LastUpdatedAt: lo.ToPtr(meta.UpdateTime),
				ProcessStatus: lo.ToPtr(string(entity.DocumentProcessStatusProcessing)),
			})
			lock.Unlock()

			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		return err
	}

	err = s.batchUpdateDocumentToES(ctx, opts)
	if err != nil {
		return err
	}

	err = s.SendBatchUpsertContentMessage(ctx, updatedDocs, false)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send batch upsert content: %v", err)
	}
	return nil
}

func (s *Service) GetKnowledgeTaskStatus(ctx context.Context, datasetID string) (string, error) {
	tasks, err := s.dao.ListKnowledgeTask(ctx, dal.ListKnowledgeTaskOption{
		DatasetID: datasetID,
		Status:    []string{string(entity.KnowledgeTaskStatusCreated), string(entity.KnowledgeTaskStatusRunning), string(entity.KnowledgeTaskStatusFailed)},
	})
	if err != nil {
		return "", err
	}

	gslice.Filter(tasks, func(task *po.NextKnowledgeTaskPO) bool {
		if task.TaskStatus == string(entity.KnowledgeTaskStatusFailed) && task.FailedCount >= entity.MaxRetryTimes {
			return false
		}

		return true
	})

	return operatorx.IfThen(len(tasks) > 0, nextagent.KnowledgeTaskStatusProcessing, nextagent.KnowledgeTaskStatusFinished), nil
}

func (s *Service) GetDocument(ctx context.Context, datasetID, documentID string) (*entity.Document, error) {
	document, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return nil, err
	}
	content, err := s.getContentFromTOS(ctx, lo.FromPtr(document.Content))
	if err != nil {
		return nil, err
	}
	document.DocumentContent = lo.ToPtr(content)
	go func() {
		err = s.add1HitCountToES(ctx, documentID)
		if err != nil {
			logs.V1.CtxError(ctx, "[GetDocument] failed to upsert document to es: %v", err)
		}
	}()

	return document, nil
}

func (s *Service) GetDocumentMetadata(ctx context.Context, datasetID, documentID string) (*entity.Document, error) {
	document, err := s.dao.GetDocument(ctx, datasetID, documentID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get document")
	}
	return document, nil
}

type ListDocumentsOption struct {
	Query         *string
	Creators      []string
	DescOrderBy   *string
	ProcessStatus []string
	Limit         int
	Offset        int
}

func (s *Service) ListDocuments(ctx context.Context, datasetID string, opt *ListDocumentsOption) ([]*entity.Document, int64, error) {
	docs, totalCount, err := s.searchDocumentsFromES(ctx, datasetID, &searchDocumentsOption{
		Query:         opt.Query,
		Creator:       opt.Creators,
		DescOrderBy:   lo.Ternary(opt.DescOrderBy != nil, opt.DescOrderBy, lo.Ternary(opt.Query == nil, lo.ToPtr(DestOrderByUpdatedAt), nil)),
		ProcessStatus: opt.ProcessStatus,
		Limit:         opt.Limit,
		Offset:        opt.Offset,
	})
	if err != nil {
		return nil, 0, err
	}

	if len(docs) == 0 {
		return nil, 0, nil
	}
	documentIDs := lo.Map(docs, func(doc *esDocumentDoc, index int) string {
		return lo.FromPtr(doc.ID)
	})
	documentMap, err := s.dao.MGetDocument(ctx, documentIDs)
	if err != nil {
		return nil, 0, err
	}
	ret := make([]*entity.Document, 0, len(documentMap))
	for _, doc := range docs {
		document, ok := documentMap[lo.FromPtr(doc.ID)]
		if !ok {
			logs.V1.CtxError(ctx, "[ListDocuments] document not found, docID: %s", lo.FromPtr(doc.ID))
			continue
		}
		ret = append(ret, &entity.Document{
			ID:            document.ID,
			DatasetID:     document.DatasetID,
			Creator:       document.Creator,
			Title:         document.Title,
			SourceType:    document.SourceType,
			SourceUid:     document.SourceUid,
			Owner:         document.Owner,
			CreatedAt:     document.CreatedAt,
			LastUpdatedAt: document.LastUpdatedAt,
			ProcessStatus: document.ProcessStatus,
			FaildReason:   document.FaildReason,
			Heat:          lo.ToPtr(doc.HitCount), // 目前热度为@搜索次数
			ContentType:   document.ContentType,
			UpdatedAt:     document.UpdatedAt,
			ImportType:    document.ImportType,
			WikiSpaceName: document.WikiSpaceName,
		})
	}
	return ret, totalCount, nil
}

type ReferenceDocument struct {
	Title string
	URL   string
}

func (s *Service) RecommendDocuments(ctx context.Context, username, datasetID, jwtToken string, referenceDocuments []*ReferenceDocument) ([]*entity.LarkDocument, error) {
	userToken, err := s.larkService.GetUserAccessToken(ctx, username)
	if err != nil {
		if db.IsRecordNotFoundError(err) || errors.Is(err, larkservice.ErrRefreshTokenExpired) {
			return nil, lark.ErrLarkAuthFailed
		}
		return nil, err
	}
	existDocs, _ := s.dao.ListDocumentsByDatasetID(ctx, datasetID)
	fileKeyMap := make(map[string]bool)
	for _, doc := range existDocs {
		fileKeyMap[doc.SourceUid] = true
	}
	for _, doc := range referenceDocuments {
		_, referenceFileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		fileKeyMap[referenceFileKey] = true
	}
	ret := make([]*entity.LarkDocument, 0, 20)
	docs := make([]lark.SearchPassage, 0, 20)
	lock := sync.Mutex{}
	pool, err := poolsdk.NewPool(2)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create pool")
	}
	defer func() {
		pool.Release()
	}()
	wg := sync.WaitGroup{}
	if len(referenceDocuments) > 5 {
		referenceDocuments = referenceDocuments[:5]
	}
	for _, doc := range referenceDocuments {
		wg.Add(1)
		_ = pool.Submit(func() {
			defer wg.Done()
			_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
			searchDocs, err := s.searchByTitle(ctx, doc.Title, fileKey, userToken, jwtToken)
			if err != nil {
				return
			}
			lock.Lock()
			docs = append(docs, searchDocs...)
			lock.Unlock()
		})
	}
	wg.Wait()
	for _, tmp := range docs {
		extra := lark.SearchPassageExtra{}
		err = json.Unmarshal([]byte(tmp.Extra), &extra)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to unmarshal extra")
		}
		contentType := entity.DocumentContentType(extra.GetContentType())
		if extra.OwnerName == "Aime" { // 过滤掉Aime的文档
			continue
		}
		if !contentType.IsSupport() {
			logs.V1.Error("not support document type: %s, url:%s, extra:%v", contentType, tmp.URL, extra)
			continue
		}
		_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(tmp.URL))
		if _, ok := fileKeyMap[fileKey]; ok { // 文档去重
			continue
		}
		tmp.Content = postContent(tmp.Content)
		ret = append(ret, &entity.LarkDocument{
			Title:       tmp.Title,
			URL:         tmp.URL,
			IsUploaded:  fileKeyMap[fileKey],
			Content:     tmp.Content,
			ContentType: string(contentType),
			OwnerName:   extra.OwnerName,
			Score:       tmp.Score,
		})
		fileKeyMap[fileKey] = true
	}
	return ret, nil
}

type SortDoc struct {
	Title string
	Token string
}

func (s *Service) searchByTitle(ctx context.Context, title, originToken, userToken, jwtToken string) ([]lark.SearchPassage, error) {
	topK := 50
	data, err := doNerReq(title)
	if err != nil {
		logs.V1.CtxError(ctx, "[searchByTitle] failed to doNerReq: %v", err)
		respData, err := s.larkCli.SearchLarkData(ctx, topK, title, userToken, &lark.PassageDisableSearch{HelpdeskDisable: true})
		if err != nil {
			logs.V1.CtxError(ctx, "[searchByTitle] failed to search documents: %v, search content:%v", err, title)
			return nil, err
		}
		return respData.Data.Passages, nil
	}
	pool, err := poolsdk.NewPool(len(data))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create pool")
	}
	defer pool.Release()
	wg := sync.WaitGroup{}
	lock := sync.Mutex{}
	newQueryBuilder := &strings.Builder{}
	for idx, d := range data {
		newQueryBuilder.WriteString(d.Text)
		if idx != len(data)-1 {
			newQueryBuilder.WriteString(" ")
		}
	}
	newQuery := newQueryBuilder.String()
	count := 10
	docSortDocs := make([]SortDoc, 0, count)
	wikiSortDocs := make([]SortDoc, 0, count)
	wg.Add(1)
	pool.Submit(func() {
		defer wg.Done()
		searchLarkObjectResp, err := s.larkCli.SearchLarkObject(ctx, &lark.SearchLarkObjectRequest{
			SearchKey: newQuery,
			Count:     count,
			Offset:    0,
			DocsTypes: []string{"doc"},
		}, userToken)
		if err != nil {
			logs.V1.CtxError(ctx, "[searchByTitle] failed to search lark object: %v", err)
		} else {
			for _, docsEntity := range searchLarkObjectResp.DocsEntities {
				log.V1.CtxInfo(ctx, "[searchByTitle] docsEntity title: %v", docsEntity.Title)
				lock.Lock()
				if docsEntity.DocsToken != originToken {
					docSortDocs = append(docSortDocs, SortDoc{
						Title: docsEntity.Title,
						Token: docsEntity.DocsToken,
					})
				}
				lock.Unlock()
			}
		}
	})
	wg.Add(1)
	_ = pool.Submit(func() {
		defer wg.Done()
		searchLarkWikiResp, err := s.larkCli.SearchWikiNode(ctx, &lark.SearchWikiNodeRequest{
			Query:    newQuery,
			PageSize: lo.ToPtr(count),
		}, userToken)
		if err != nil {
			logs.V1.CtxError(ctx, "[searchByTitle] failed to search wiki node: %v", err)
		} else {
			for _, node := range searchLarkWikiResp.Items {
				log.V1.CtxInfo(ctx, "[searchByTitle] node title: %v", node.Title)
				lock.Lock()
				if node.NodeID != originToken {
					wikiSortDocs = append(wikiSortDocs, SortDoc{
						Title: node.Title,
						Token: node.NodeID,
					})
				}
				lock.Unlock()
			}
		}
	})
	wg.Wait()
	if len(docSortDocs) == 0 && len(wikiSortDocs) == 0 {
		return nil, nil
	}
	sortDocs := mergeAlternately(docSortDocs, wikiSortDocs)
	sortDocs = s.sortTokensByTitlesSimilarity(ctx, jwtToken, title, sortDocs)
	docMap := make(map[string]lark.SearchPassage)
	//docMap, err = s.searchLarkData(ctx, title, userToken, docSortDocs, wikiSortDocs, docMap)
	titles := make([]string, 0, len(sortDocs))
	for _, sortDoc := range sortDocs {
		titles = append(titles, sortDoc.Title)
	}
	docMap, err = s.searchLarkData(ctx, strings.Join(titles, ","), userToken, docSortDocs, wikiSortDocs, docMap)
	if err != nil {
		return nil, err
	}
	docs := make([]lark.SearchPassage, 0, len(docMap))
	for _, sortDoc := range sortDocs {
		if doc, ok := docMap[sortDoc.Token]; ok {
			docs = append(docs, doc)
			delete(docMap, sortDoc.Token)
		}
	}
	// If too few documents.
	dedupDocs := make(map[string]bool)
	for _, doc := range docs {
		dedupDocs[doc.URL] = true
	}
	if len(docs) < topK {
		for _, doc := range docMap {
			if dedupDocs[doc.URL] {
				continue
			}
			docs = append(docs, doc)
			if len(docs) == topK {
				break
			}
			dedupDocs[doc.URL] = true
		}
	}
	return docs, nil
}

func (s *Service) searchLarkData(ctx context.Context, query, userToken string, docSortDocs, wikiSortDocs []SortDoc, docMap map[string]lark.SearchPassage) (map[string]lark.SearchPassage, error) {
	topK := 50
	searchQuery := fmt.Sprintf("帮我找些 title 和 '%s' 有关的信息", query)
	respData, err := s.larkCli.SearchLarkData(ctx, topK, searchQuery, userToken,
		&lark.PassageDisableSearch{HelpdeskDisable: true, DocTokens: lo.Map(docSortDocs, func(item SortDoc, index int) string {
			return item.Token
		}), WikiNodeTokens: lo.Map(wikiSortDocs, func(item SortDoc, index int) string {
			return item.Token
		})})
	if err != nil {
		logs.V1.CtxError(ctx, "[searchByTitle] failed to search documents: %v, search content:%v", err, searchQuery)
		return nil, err
	}
	for _, doc := range respData.Data.Passages {
		_, fileKey := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.URL))
		docMap[fileKey] = doc
	}
	return docMap, nil
}

type sortSimilarity struct {
	similarity float64
	Index      int
}

func (s *Service) sortTokensByTitlesSimilarity(ctx context.Context, jwtToken string, query string, sortDocs []SortDoc) []SortDoc {
	queries := make([]string, 0, len(sortDocs)+1)
	queries = append(queries, query)
	for _, doc := range sortDocs {
		queries = append(queries, doc.Title)
	}
	result, err := s.embeddings(ctx, jwtToken, queries)
	if err != nil {
		logs.V1.CtxError(ctx, "[sortTokensByTitlesSimilarity] failed to get embeddings: %v", err)
		return sortDocs
	}
	if len(result) != len(queries) {
		logs.V1.CtxError(ctx, "[sortTokensByTitlesSimilarity] failed to get embeddings, result length not equal to queries length")
		return sortDocs
	}
	similarities := make([]sortSimilarity, 0, len(result)-1)
	for i := 1; i < len(result); i++ {
		tmp := sortSimilarity{
			similarity: cosineSimilarity(result[0], result[i]),
			Index:      i - 1,
		}
		similarities = append(similarities, tmp)
	}
	sort.Slice(similarities, func(i, j int) bool {
		return similarities[i].similarity > similarities[j].similarity
	})
	newSortDocs := make([]SortDoc, 0, len(sortDocs))
	for _, similarity := range similarities {
		newSortDocs = append(newSortDocs, sortDocs[similarity.Index])
	}
	return newSortDocs
}

func postContent(content string) string {
	content = strings.ReplaceAll(content, "[title]", "")
	content = strings.ReplaceAll(content, "[block_sep]", "\n")
	re := regexp.MustCompile(`\[heading\d*\]`)
	content = re.ReplaceAllString(content, "#")
	re = regexp.MustCompile(`(^|\n)\[content\]`)
	content = re.ReplaceAllString(content, "")
	content = strings.ReplaceAll(content, "[content]", "\n")
	return content
}

func (s *Service) CountDocuments(ctx context.Context, datasetID, username string) (int, int, error) {
	_, allTotal, err := s.searchDocumentsFromES(ctx, datasetID, nil)
	if err != nil {
		return 0, 0, err
	}
	_, myTotal, err := s.searchDocumentsFromES(ctx, datasetID, &searchDocumentsOption{Creator: []string{username}})
	if err != nil {
		return 0, 0, err
	}
	return int(allTotal), int(myTotal), nil
}

type CreateKnowledgeAsyncOpts struct {
	DatasetID  string
	Username   string
	TaskType   entity.KnowledgeTaskType
	WikiDocUrl string
	SpaceName  string
}

func (s *Service) CreateKnowledgeAsync(ctx context.Context, opts CreateKnowledgeAsyncOpts) error {
	switch opts.TaskType {
	case entity.KnowledgeTaskTypeImportWiki:
		taskID := s.idGen.NewID()
		params, _ := json.Marshal(&entity.ImportWikiParams{
			WikiDocUrl: opts.WikiDocUrl,
			SpaceName:  opts.SpaceName,
		})
		err := s.dao.CreateKnowledgeTask(ctx, []*po.NextKnowledgeTaskPO{
			{
				Uid:        taskID,
				DatasetID:  opts.DatasetID,
				Creator:    opts.Username,
				TaskType:   string(entity.KnowledgeTaskTypeImportWiki),
				TaskStatus: string(entity.KnowledgeTaskStatusCreated),
				Params:     lo.ToPtr(string(params)),
			},
		})
		if err != nil {
			return err
		}

		eventMsg := entity.KnowledgebaseEvent{
			ImportWikiEvent: &entity.ImportWikiEvent{
				TaskID:     taskID,
				DatasetID:  opts.DatasetID,
				Creator:    opts.Username,
				WikiDocUrl: opts.WikiDocUrl,
				SpaceName:  opts.SpaceName,
			},
		}

		msgBytes, _ := json.Marshal(eventMsg)
		return s.knowledgebaseMQ.SendMessage(ctx, msgBytes, agententity.KnowledgebaseTag)
	default:
		return errors.New("unsupported task type")
	}
}

func (s *Service) CreateLarkDocSync(ctx context.Context, larkDocConfig *nextagent.LarkDocConfig, datasetID, username string) ([]*entity.Document, error) {
	var opts []UploadDocumentOption
	for _, docUrl := range larkDocConfig.GetSingleDocs() {
		sourceTypeStr, sourceUid := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(docUrl))

		sourceType, ok := entity.GetDocumentSourceType(sourceTypeStr)
		if !ok {
			logs.V1.CtxWarn(ctx, "[createLarkDoc] SingleDocs,invalid lark doc url: %s", docUrl)
			continue
		}

		opts = append(opts, UploadDocumentOption{
			SourceType: sourceType,
			SourceUid:  sourceUid,
			ImportType: entity.ImportTypeSingle,
		})
	}

	for _, wiki := range larkDocConfig.GetWikiList() {
		docsSingleWikiSpace := gslice.FilterMap(wiki.GetDocs(), func(doc *nextagent.WikiDoc) (UploadDocumentOption, bool) {
			if doc.GetSelectAll() {
				return UploadDocumentOption{}, false
			}

			sourceType, sourceUid := devaiutil.ParseLarkDocURL(ctx, strings.TrimSpace(doc.GetUrl()))
			if sourceType != "wiki" {
				logs.V1.CtxWarn(ctx, "[createLarkDoc] WikiList, invalid lark doc url: %s", doc.GetUrl())
				return UploadDocumentOption{}, false
			}

			return UploadDocumentOption{
				SourceType:    entity.DocumentSourceTypeLarkWiki,
				SourceUid:     sourceUid,
				ImportType:    entity.ImportTypeWikiTree,
				WikiSpaceName: wiki.GetWikiSpace().GetSpaceName(),
			}, true
		})

		opts = append(opts, docsSingleWikiSpace...)
	}

	docs, err := s.UploadDocuments(ctx, datasetID, username, opts)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to upload documents")
	}

	return docs, nil
}

func (s *Service) CreateLarkDocAsync(ctx context.Context, larkDocConfig *nextagent.LarkDocConfig, datasetID, username string) error {
	for _, wikiDocConfig := range larkDocConfig.GetWikiList() {
		if wikiDocConfig.GetWikiSpace() == nil {
			continue
		}

		for _, doc := range wikiDocConfig.GetDocs() {
			if !doc.GetSelectAll() {
				continue
			}

			err := s.CreateKnowledgeAsync(ctx, CreateKnowledgeAsyncOpts{
				DatasetID:  datasetID,
				Username:   username,
				TaskType:   entity.KnowledgeTaskTypeImportWiki,
				WikiDocUrl: doc.GetUrl(),
				SpaceName:  wikiDocConfig.GetWikiSpace().GetSpaceName(),
			})
			if err != nil {
				return errors.WithMessage(err, "failed to create async task")
			}

		}
	}

	return nil
}
