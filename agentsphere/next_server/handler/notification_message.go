package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/samber/lo"
)

func (h *Handler) CreateNotificationMessage(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateNotificationMessageRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "[CreateNotificationMessage] request: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account: *user,
		Action:  entity.PermissionActionSpaceCreate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	messageID, err := h.NotificationService.CreateNotificationMessage(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[CreateNotificationMessage] failed to create NotificationMessage: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create NotificationMessage")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateNotificationMessageResponse{
		MessageID: messageID,
	})
}

func (h *Handler) ListNotificationMessage(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListNotificationMessagesRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "[ListNotificationMessage] request: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	messages, err := h.NotificationService.ListNotificationMessage(ctx, user.Username, user.Department)
	if err != nil {
		log.V1.CtxError(ctx, "[ListNotificationMessage] failed to list messages: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list messages")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListNotificationMessagesResponse{
		Messages: messages,
	})
}

func (h *Handler) UpdateNotificationMessageStatus(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateNotificationMessageStatusRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "[UpdateNotificationMessageStatus] request: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	err := h.NotificationService.UpdateNotificationMessageStatus(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[UpdateNotificationMessageStatus] failed to update message status: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update message status")
		return
	}

	c.JSON(http.StatusOK, nextagent.UpdateNotificationMessageStatusResponse{})
}
