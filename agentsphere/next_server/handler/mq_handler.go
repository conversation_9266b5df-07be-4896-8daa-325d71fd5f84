package serverhandler

import (
	"context"
	"encoding/json"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	testingservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/testing"

	"code.byted.org/gopkg/logs/v2/log"
)

func (h *Handler) StartConsumer() error {
	h.SessionMonitorMQ.RegisterHandler(h.HandleServerMonitorEvent)
	return h.SessionMonitorMQ.StartConsumer()
}

func (h *Handler) StopConsumer() error {
	return h.SessionMonitorMQ.Close()
}

func (h *Handler) HandleServerMonitorEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received session monitor event: %s", string(msg))
	m := entity.ServerMonitorEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch m.EventName {
	case entity.MonitorEventTypeSessionCheck:
		if m.SessionCheckEvent != nil {
			err = h.MonitorService.CheckSession(ctx, m.SessionCheckEvent.SessionID)
		} else {
			log.V1.CtxError(ctx, "session check event is nil")
		}
	case entity.MonitorEventTypeTemplateCheck:
		if m.TemplateCheckEvent != nil {
			err = h.MonitorService.CheckTemplate(ctx, m.TemplateCheckEvent.TemplateID)
		}
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle session monitor event: %v", err)
		return err
	}
	return nil
}

func (h *Handler) KnowledgebaseStartConsumer() error {
	h.KnowledgebaseMQ.RegisterHandler(h.HandleKnowledgebaseEvent)
	return h.KnowledgebaseMQ.StartConsumer()
}

func (h *Handler) KnowledgebaseStopConsumer() error {
	return h.KnowledgebaseMQ.Close()
}

func (h *Handler) HandleKnowledgebaseEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received knowledge base event: %s", string(msg))
	m := entity.KnowledgebaseEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch {
	case m.DocumentContentEvent != nil:
		err = h.KnowledgebaseService.UpsertContent(ctx, m.DocumentContentEvent)
	case m.ImportWikiEvent != nil:
		err = h.KnowledgebaseService.ImportWiki(ctx, m.ImportWikiEvent)
	default:
		log.V1.CtxError(ctx, "knowledge base event is nil")
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle knowledge base event: %v", err)
		return err
	}
	return nil
}

func (h *Handler) TraceStartConsumer() error {
	h.TraceMQ.RegisterHandler(h.HandleTraceEvent)
	return h.TraceMQ.StartConsumer()
}

func (h *Handler) TraceStopConsumer() error {
	return h.TraceMQ.Close()
}

func (h *Handler) HandleTraceEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received trace event: %s", string(msg))
	m := entity.NextTraceEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch {
	case m.TraceData != nil:
		err = h.DebugService.UpsertTrace(ctx, m)
	default:
		log.V1.CtxError(ctx, "trace event is nil")
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle trace event: %v", err)
		// skip return error to avoid retry
		return nil
	}
	return nil
}

func (h *Handler) DeployReviewStartConsumer() error {
	h.DeployReviewMQ.RegisterHandler(h.HandleDeployReviewEvent)
	return h.DeployReviewMQ.StartConsumer()
}

func (h *Handler) DeployReviewStopConsumer() error {
	return h.DeployReviewMQ.Close()
}

func (h *Handler) HandleDeployReviewEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received deploy review event: %s", string(msg))
	m := entity.DeployReviewEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	// 查询灰度比例，运行上线流水线
	err := h.DeployReviewService.HandleWorkflowProcess(ctx, &m)
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle deploy review event: %v", err)
		return err
	}

	return nil
}

func (h *Handler) TestingMonitorStartConsumer() error {
	h.TestingMQ.RegisterHandler(h.HandleTestingMonitorEvent)
	return h.TestingMQ.StartConsumer()
}

func (h *Handler) TestingMonitorStopConsumer() error {
	return h.TestingMQ.Close()
}

func (h *Handler) HandleTestingMonitorEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received testing event: %s", string(msg))
	m := testingservice.RecycleContainerEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	err := h.TestingService.HandleTestingMonitorEvent(ctx, &m)
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle testing monitor event: %v", err)
		return err
	}

	return nil
}
