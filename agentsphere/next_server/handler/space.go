package serverhandler

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/page"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
)

// CreateSpace handles the creation of a new space.
// @router /api/next/v1/space [POST]
func (h *Handler) CreateSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create space request: %+v", req)

	if req.Name == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "space name is required")
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// TODO: use middleware is better
	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account: *user,
		Action:  entity.PermissionActionSpaceCreate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	space, err := h.SpaceService.CreateSpace(ctx, spaceservice.CreateSpaceOption{
		Name:        req.Name,
		NameEN:      req.GetNameEN(),
		Description: req.GetDescription(),
		UserName:    user.Username,
		Type:        entity.SpaceTypeProject, // Default to project type
		Config:      entity.ParseSpaceConfigFromIDL(req.SpaceConfig),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create space: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create space")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateSpaceResponse{
		Space: getSpaceFromEntity(space),
	})
}

func (h *Handler) CreatePersonalSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreatePersonalSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create space request: %+v", req)

	if req.UserName == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "space name is required")
		return
	}

	_, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	space, err := h.SpaceService.CreatePersonalSpace(ctx, req.UserName)
	if err != nil {
		log.V1.CtxError(ctx, "failed to create space: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create space")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreatePersonalSpaceResponse{
		Space: getSpaceFromEntity(space),
	})
}

// UpdateSpace handles updating an existing space.
func (h *Handler) UpdateSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update space request: %+v", req)

	if req.GetSpaceId() == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "space ID and name are required")
		return
	}

	_, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	space, err := h.SpaceService.UpdateSpace(ctx, spaceservice.UpdateSpaceOption{
		SpaceID:     req.SpaceId,
		Name:        req.Name,
		NameEN:      req.NameEN,
		Description: req.Description,
		Config:      entity.ParseSpaceConfigFromIDL(req.SpaceConfig),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update space: %v", err)
		if errors.Is(err, serverservice.ErrSpaceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update space")
		}
		return
	}

	c.JSON(http.StatusOK, nextagent.UpdateSpaceResponse{
		Space: getSpaceFromEntity(space),
	})
}

var (
	DebugSpace = "x-debug-space"
)

// GetSpace handles retrieving a space by its ID.
func (h *Handler) GetSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get space request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	spaceID := req.GetSpaceId()
	resourceType := entity.ResourceTypeSpace

	// 如果 type 和 external 有值，优先返回该 space
	if req.Type != nil && req.ExternalID != nil {
		resource, err := h.PermissionService.GetResource(ctx, permissionservice.GetResourceOption{
			ResourceExternalID: req.ExternalID,
			ResourceType:       lo.ToPtr(entity.ResourceType(*req.Type)),
			NeedGroup:          true,
		})
		if err != nil || resource == nil || len(resource.GroupRelation) == 0 {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get resource")
			log.V1.CtxError(ctx, "failed to get resource: %v", err)
			return
		}
		spaceID = resource.GroupRelation[0].GroupID
	} else if spaceID == "" {
		// 返回默认个人空间
		space, err := h.SpaceService.GetPersonalSpace(ctx, spaceservice.GetPersonalSpaceOption{
			Owner: user.Username,
		})
		if err != nil {
			if errors.Is(err, serverservice.ErrSpaceNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "user space not found")
				return
			}
			if errors.Is(err, serverservice.ErrDatasetNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "user space dataset not found")
				return
			}
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get personal space")
			log.V1.CtxError(ctx, "failed to get personal space: %v", err)
			return
		}
		// 自己一定有自己的空间权限
		c.JSON(http.StatusOK, nextagent.GetSpaceResponse{
			Space:          getSpaceFromEntity(space),
			HavePermission: lo.ToPtr(true),
		})
		return
	}

	havePermission := true
	perm, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: &spaceID,
		ResourceType:       &resourceType,
		Action:             entity.PermissionActionSpaceRead,
	})
	if err != nil {
		if errors.Is(err, permissionservice.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
			return
		}
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if perm == nil {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "forbidden")
		return
	}

	var (
		space *entity.Space
	)
	if !perm.Allowed {
		// 权限校验不通过，返回用户自己的个人空间
		havePermission = false
		space, err = h.SpaceService.GetPersonalSpace(ctx, spaceservice.GetPersonalSpaceOption{
			Owner: user.Username,
		})
	} else {
		space, err = h.SpaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
			SpaceID:     spaceID,
			NeedDataset: true,
			NeedMembers: req.GetNeedMembers(),
		})
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to get space: %v", err)
		if errors.Is(err, serverservice.ErrSpaceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get space")
		}
		return
	}

	// 特殊逻辑：如果访问的是他人的个人空间，则默认返回请求人的个人空间
	if space.Type.IsPersonal() && space.Creator != user.Username && c.Request.Header.Get(DebugSpace) == "" {
		space, err = h.SpaceService.GetPersonalSpace(ctx, spaceservice.GetPersonalSpaceOption{
			Owner: user.Username,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get personal space: %v", err)
			if errors.Is(err, serverservice.ErrSpaceNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
			} else {
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get personal space")
			}
			return
		}
	}

	c.JSON(http.StatusOK, nextagent.GetSpaceResponse{
		Space:          getSpaceFromEntity(space),
		HavePermission: &havePermission,
	})
}

// DeleteSpace handles deleting a space by its ID.
func (h *Handler) DeleteSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete space request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: &req.SpaceId,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDelete,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.SpaceService.DeleteSpace(ctx, req.SpaceId, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete space: %v", err)
		if errors.Is(err, serverservice.ErrSpaceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete space")
		}
		return
	}

	// TODO: 支持返回success和fail的list
	c.JSON(http.StatusOK, nextagent.DeleteSpaceResponse{
		Success: true,
	})
}

// ListSpaces handles listing spaces.
func (h *Handler) ListAllProjectSpaces(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListAllSpacesRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list spaces request: %+v", req)

	_, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// permRes, err := h.PermissionService.CheckPermission(ctx, permission.CheckPermissionOption{
	// 	Account: *user,
	// 	// ResourceType: lo.ToPtr(entity.ResourceTypeSpace),
	// 	Action: entity.PermissionActionSpaceRead,
	// })
	// if err != nil || permRes == nil || !permRes.Allowed {
	// 	message := lo.Ternary(err != nil, err.Error(), "permission denied")
	// 	log.V1.CtxError(ctx, "failed to check permission: %v", message)
	// 	hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
	// 	return
	// }

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.GetPageNum()), int(req.GetPageSize()))
	spaces, total, err := h.SpaceService.ListAllProjectSpaces(ctx, spaceservice.ListAllProjectSpacesOption{
		Offset: offset,
		Limit:  limit,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list spaces: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list spaces")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListAllSpacesResponse{
		Spaces: lo.Map(spaces, func(s *entity.Space, _ int) *nextagent.Space {
			return getSpaceFromEntity(s)
		}),
		Total: total,
	})
}

func (h *Handler) ListUserSpaces(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListUserSpacesRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list spaces request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	spaces, nextID, err := h.SpaceService.ListUserSpaces(ctx, spaceservice.ListUserSpacesOption{
		Account: *user,
		StartID: req.StartID,
		Limit:   int(req.GetLimit()),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list spaces: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list spaces")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListUserSpacesResponse{
		Spaces: lo.Map(spaces, func(s *entity.Space, _ int) *nextagent.Space {
			return getSpaceFromEntity(s)
		}),
		NextID: nextID,
	})
}

// AddSpaceMember handles adding a member to a space.
func (h *Handler) AddSpaceMembers(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.AddSpaceMemberRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "add space member request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.SpaceId),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	// is space deleted
	_, err = h.SpaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
		SpaceID: req.SpaceId,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get space: %v", err)
		if errors.Is(err, serverservice.ErrSpaceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get space")
		}
		return
	}

	toAddMembers := lo.Map(req.Members, func(m *nextagent.SpaceMember, _ int) *entity.SpaceMember {
		return &entity.SpaceMember{
			Name: m.Name,
			Type: entity.PermissionType(m.Type),
			Role: entity.PermissionRole(m.Role),
		}
	})
	// 去重
	toAddMembers = lo.UniqBy(toAddMembers, func(m *entity.SpaceMember) string {
		return fmt.Sprintf("%s-%v-%v", m.Name, m.Type, m.Role)
	})

	_, err = h.SpaceService.AddSpaceMembers(ctx, spaceservice.AddSpaceMembersOption{
		SpaceID: req.SpaceId,
		Members: toAddMembers,
		Actor:   user.Username,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to add space member: %v", err)
		if db.IsDuplicateKeyError(err) {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "member already exists")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to add space members")
		}
		return
	}

	c.JSON(http.StatusOK, nextagent.AddSpaceMemberResponse{
		Success: true,
	})
}

// RemoveSpaceMember handles removing a member from a space.
func (h *Handler) RemoveSpaceMembers(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RemoveSpaceMemberRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "remove space member request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.SpaceId),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	toRemoveMembers := lo.Map(req.Members, func(m *nextagent.SpaceMember, _ int) *entity.SpaceMember {
		return &entity.SpaceMember{
			Name: m.Name,
			Type: entity.PermissionType(m.Type),
			Role: entity.PermissionRole(m.Role),
		}
	})
	toRemoveMembers = lo.UniqBy(toRemoveMembers, func(m *entity.SpaceMember) string {
		return fmt.Sprintf("%s-%v-%v", m.Name, m.Type, m.Role)
	})
	err = h.SpaceService.RemoveSpaceMembers(ctx, spaceservice.RemoveSpaceMembersOption{
		SpaceID: req.SpaceId,
		Actor:   user.Username,
		Members: toRemoveMembers,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to remove space member: %v", err)
		if errors.Is(err, permissionservice.ErrNotAllowedRemoveOwner) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "cannot remove owner from space")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to remove space member")
		return
	}

	c.JSON(http.StatusOK, nextagent.RemoveSpaceMemberResponse{
		Success: true,
	})
}

// ListSpaceMembers handles listing members of a space.
func (h *Handler) ListSpaceMembers(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSpaceMembersRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list space members request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.SpaceId),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceRead,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	var role *entity.PermissionRole
	if req.Role != nil {
		role = lo.ToPtr(entity.PermissionRole(*req.Role))
	}
	offset, limit := page.GetOffsetAndLimitFromPage(int(req.GetPageNum()), int(req.GetPageSize()))

	members, total, err := h.SpaceService.ListSpaceMembers(ctx, spaceservice.ListSpaceMembersOption{
		SpaceID: req.SpaceId,
		Offset:  offset,
		Limit:   limit,
		Role:    role,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list space members: %v", err)
		if errors.Is(err, permissionservice.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list space members")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListSpaceMembersResponse{
		Members: lo.Map(members, func(m *entity.SpaceMember, _ int) *nextagent.SpaceMember {
			return getSpaceMemberFromEntity(m)
		}),
		Total: total,
	})
}

func getSpaceFromEntity(space *entity.Space) *nextagent.Space {
	if space == nil {
		return nil
	}
	s := &nextagent.Space{
		ID:          space.ID,
		Name:        space.Name,
		NameEN:      &space.NameEN,
		Description: &space.Description,
		Creator:     space.Creator,
		Type:        string(space.Type),
		Status:      string(space.Status),
		CreatedAt:   space.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   space.UpdatedAt.Format(time.RFC3339),
		DataSetID:   space.DataSetID,
		Members: lo.Map(space.Members, func(m *entity.SpaceMember, _ int) *nextagent.SpaceMember {
			return getSpaceMemberFromEntity(m)
		}),
		PermissionActions: lo.Map(space.PermissionActions, func(a entity.PermissionAction, _ int) nextagent.PermissionAction {
			return nextagent.PermissionAction(a)
		}),
	}
	if space.Config != nil {
		s.SpaceConfig = space.Config.ToIDL()
	}
	return s
}

func getSpaceMemberFromEntity(member *entity.SpaceMember) *nextagent.SpaceMember {
	if member == nil {
		return nil
	}
	return &nextagent.SpaceMember{
		Name: member.Name,
		Type: nextagent.PermissionType(member.Type),
		Role: nextagent.PermissionRole(member.Role),
	}
}

// func (h *Handler) checkSpacePermission(ctx, user *entity.Account, spaceID string, action entity.PermissionAction) (*permission.PermissionResult, error) {
// 	resourceType := entity.ResourceTypeSpace
// 	permRes, err := h.PermissionService.CheckPermission(ctx, permission.CheckPermissionOption{
// 		Account:            *user,
// 		ResourceExternalID: &spaceID,
// 		ResourceType:       &resourceType,
// 		Action:             action,
// 	})
// 	if err != nil {
// 		log.V1.CtxError(ctx, "failed to check permission for user %s on space %s: %v", user.Username, spaceID, err)
// 		return nil, errors.Wrap(err, "failed to check permission")
// 	}
// 	return permRes, nil
// }

func (h *Handler) InitSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.InitSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "init space request: %v", req)

	if req.GetSpaceID() == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "space ID are required")
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.SpaceService.InitSpace(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to init space: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to init space")
		return
	}

	c.JSON(http.StatusOK, nextagent.InitSpaceResponse{})
}
