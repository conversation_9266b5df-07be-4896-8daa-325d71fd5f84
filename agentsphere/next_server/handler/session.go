package serverhandler

import (
	"context"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentservice"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/page"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/db"
)

const (
	debugAgentVersionHeaderKey       = "x-debug-version"
	debugNotUsePreparedCubeHeaderKey = "forbid-prepare-cube"
	debugTTEnvHeaderKey              = "x-tt-env"
	sessionSpaceIDKey                = "session-space-id"
	disableTestingHeaderKey          = "x-disable-testing"
	// Use for e2e testing from local.
	testingHeaderKey = "x-testing"
)

func (h *Handler) CreateSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateSessionRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create session request: %+v, referer: %s, agent: %s, origin: %s, remote addr: %s",
		req, c.Request.Header.Get("referer"), c.Request.Header.Get("origin"), string(c.Request.Header.UserAgent()), c.RemoteAddr().String())
	logID, _ := ctxvalues.LogID(ctx)

	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 1.1. 这里是用户发起的会话，查询用户所有已激活的mcp，组装本次会话使用的MCPs
	mcps, _, err := h.MCPService.ListSpaceMCP(ctx, &mcpservice.ListSpaceMCPOption{
		SpaceID:     req.GetSpaceID(),
		IsActive:    gptr.Of(true),
		User:        user,
		SessionRole: req.Role,
		MCPSourceTabs: []nextagent.MCPSourceTab{
			nextagent.MCPSourceTab_Custom,
			nextagent.MCPSourceTab_Builtin,
		},
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	// 1.2 剔除请求中声明需要剔除的MCPs
	excludeMCPMap := gslice.ToMap(req.ExcludedMCPs, func(f *nextagent.MCPKey) (entity.MCPKey, bool) {
		return entity.MCPKey{
			MCPID:  f.ID,
			Source: entity.MCPSource(f.Source),
		}, true
	})
	mcps = gslice.Filter(mcps, func(m *entity.MCP) bool {
		return !excludeMCPMap[m.MCPKey]
	})

	// 创建会话
	session := h.createSession(ctx, c, user, createSessionOption{
		Role:            req.Role,
		UseInternalTool: req.UseInternalTool,
		// 2.注入mcp
		MCPs:    mcps,
		SpaceID: req.SpaceID,
		LogID:   logID,
	})
	if session == nil {
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateSessionResponse{
		Session: getSessionFromEntity(session, nil),
	})
}

type createSessionOption struct {
	Role            *nextagent.SessionRole
	UseInternalTool *bool
	MCPs            []*entity.MCP
	TemplateID      string
	SpaceID         *string
	LogID           string
}

func (h *Handler) createSession(ctx context.Context, c *app.RequestContext, user *authentity.Account, opt createSessionOption) *entity.Session {
	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return nil
	}

	role := entity.SessionRoleFromIDL(opt.Role)
	if role != nil && *role == entity.SessionRoleUnknown {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "param invalid")
		return nil
	}
	useInternalTool := opt.UseInternalTool
	// 新锐青年和社招新人默认开启内部工具
	if role != nil && (*role == entity.SessionRoleYoungTalent || *role == entity.SessionRoleLateralHire) {
		useInternalTool = lo.ToPtr(true)
	}
	debugAgentVersion := c.Request.Header.Get(debugAgentVersionHeaderKey)
	disableTesting := c.Request.Header.Get(disableTestingHeaderKey)

	if opt.SpaceID == nil {
		opt.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
	}
	if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account: user,
		SpaceID: opt.SpaceID,
		Action:  entity.PermissionActionSessionCreate,
	}); !ok {
		return nil
	}

	session, err := h.SessionService.CreateSession(ctx, sessionservice.CreateSessionOption{
		User: user,
		Context: entity.SessionContext{
			UseInternalTool: useInternalTool,
			MCPs:            opt.MCPs,
		},
		Role:            role,
		SkipPrepareCube: getSkipPrepareCube(c),
		AgentVersion:    debugAgentVersion,
		TemplateID:      opt.TemplateID,
		SpaceID:         *opt.SpaceID,
		LogID:           opt.LogID,
		DisableTesting:  disableTesting != "",
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrMaximumRunningSessionsReached) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedUpperLimit), "maximum running sessions reached")
			return nil
		}
		if errors.Is(err, serverservice.ErrGlobalMaximumRunningSessionsReached) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedGlobalLimit), "global maximum sessions reached")
			return nil
		}
		if errors.Is(err, serverservice.ErrNotAllowedUseInternalTool) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentNotAllowedUseInternalTool), "not allowed use internal tool")
			return nil
		}
		if errors.Is(err, serverservice.ErrAgentVersionNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "agent version not found")
			return nil
		}
		log.V1.CtxError(ctx, "failed to create session : %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return nil
	}

	// create session resource
	var isSpacePublic *bool
	if opt.SpaceID != nil {
		space, err := h.SpaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
			SpaceID:    *opt.SpaceID,
			NeedConfig: true,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get space: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return nil
		}
		if space != nil && space.Config != nil && space.Config.BaseConfig != nil && pointer.Get(space.Config.BaseConfig.SessionVisibility) {
			isSpacePublic = lo.ToPtr(true)
		}
	}
	err = backoff.Retry(func() error {
		_, err = h.PermissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
			Owner:         user.Username,
			Type:          entity.ResourceTypeSession,
			ExternalID:    session.ID,
			GroupID:       opt.SpaceID,
			IsSpacePublic: isSpacePublic,
		})
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 2))
	if err != nil {
		log.V1.CtxError(ctx, "failed to create resource: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return nil
	}

	activity, _ := h.ActivityService.GetCurrentActivity(ctx)
	if activity != nil && activity.IsActivated() {
		err = h.ActivityService.HandleActivityCreateSession(ctx, user.Username, activity.ID)
		// 如果任务失败，打印日志，不影响createSession接口
		if err != nil {
			log.V1.CtxError(ctx, "failed to handle activity when create session: %v", err)
		}
	}

	return session
}

func getSkipPrepareCube(c *app.RequestContext) bool {
	var (
		forbidPrepareCube = c.Request.Header.Get(debugNotUsePreparedCubeHeaderKey)
		debugAgentVersion = c.Request.Header.Get(debugAgentVersionHeaderKey)
		xTTEnv            = c.Request.Header.Get(debugTTEnvHeaderKey)
	)
	// 指定了这几个 header 的请求都不走预热容器
	return forbidPrepareCube != "" || debugAgentVersion != "" || xTTEnv != ""
}

func (h *Handler) GetSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetSessionRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get session request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	perActions, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		// SpaceID:   h.getSessionSpaceID(ctx, c),
		Action: entity.PermissionActionSessionRead,
	})
	if !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		Account:   user,
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	session.PermissionActions = perActions

	star, err := h.SessionService.GetSessionStarByKey(ctx, session.ID, user.Username)
	if err != nil && !db.IsRecordNotFoundError(err) {
		log.V1.CtxError(ctx, "failed to get session star: %v", err)
	}
	if star != nil {
		session.Starred = true
	}

	messages, err := h.SessionService.ListMessages(ctx, req.SessionID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to list messages : %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list messages")
		return
	}

	c.JSON(http.StatusOK, nextagent.GetSessionResponse{
		Session: getSessionFromEntity(session, nil),
		Messages: lo.Map(messages, func(m *entity.Message, _ int) *nextagent.Message {
			return getMessageFromEntity(m)
		}),
	})
}

func (h *Handler) ListSessions(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSessionsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list sessions request: %+v", req)

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))

	user, _ := h.AuthM.GetAccount(ctx, c)

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	total, sessions, err := h.SessionService.ListSessions(ctx, sessionservice.ListSessionsOption{
		User:                 user,
		Offset:               offset,
		Limit:                limit,
		OrderByLastMessageAt: lo.ToPtr(true),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list sessions: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list sessions")
		return
	}

	sessionFirstMsgMap, err := h.batchGetSessionUserFirstMsg(ctx, sessions)
	if err != nil {
		// only log
		log.V1.CtxError(ctx, "failed to batch get session first msg: %+v", err)
	}

	c.JSON(http.StatusOK, nextagent.ListSessionsResponse{
		Sessions: lo.Map(sessions, func(s *entity.Session, _ int) *nextagent.Session {
			return getSessionFromEntity(s, sessionFirstMsgMap)
		}),
		Total: total,
	})

}

func (h *Handler) ListUserSessions(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListUserSessionsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list sessions request: %+v", req)

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))

	user, _ := h.AuthM.GetAccount(ctx, c)

	if req.AgentConfigVersionID != nil {
		if req.StartTime == nil || req.Endtime == nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "startTime and endTime must be set when agentConfigVersionID is set")
			return
		}
	}

	// 校验 startDate 和 endData 不能超过 3 天
	if req.StartTime != nil && req.Endtime != nil {
		startTime, err := time.Parse(time.DateOnly, *req.StartTime)
		if err != nil {
			// 尝试使用 DateTime 格式解析
			startTime, err = time.Parse(time.DateTime, *req.StartTime)
			if err != nil {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to parse startTime")
				return
			}
		}
		endTime, err := time.Parse(time.DateOnly, *req.Endtime)
		if err != nil {
			// 尝试使用 DateTime 格式解析
			endTime, err = time.Parse(time.DateTime, *req.Endtime)
			if err != nil {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to parse endtime")
				return
			}
		}
		if endTime.Sub(startTime) > 3*24*time.Hour {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "startTime and endTime must be within 3 days")
			return
		}
	}
	var status *entity.SessionStatus
	if req.Status != nil {
		status = entity.SessionStatusFromIDL(req.Status)
	}

	total, sessions, err := h.SessionService.ListUserSessions(ctx, sessionservice.ListUserSessionsOption{
		Offset:               offset,
		Limit:                limit,
		AgentConfigVersionID: req.AgentConfigVersionID,
		StartTime:            req.StartTime,
		Endtime:              req.Endtime,
		Operator:             user,
		Status:               status,
		OrderByCreatedAt:     lo.ToPtr(true),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list sessions: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list sessions")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListUserSessionsResponse{
		Sessions: lo.Map(sessions, func(session *entity.Session, _ int) *nextagent.UserSession {
			canNotResumeReason := lo.ToPtr(session.CanNotResumeReason.ToIDL())
			if session.CanResume {
				canNotResumeReason = nil
			}
			return &nextagent.UserSession{
				ID:                 session.ID,
				Status:             getSessionStatus(session.Status),
				Role:               session.Role.ToIDL(),
				CreatedAt:          session.CreatedAt.Format(time.RFC3339),
				UpdatedAt:          session.UpdatedAt.Format(time.RFC3339),
				Metadata:           &nextagent.SessionRuntimeMetadata{LogID: &session.RuntimeMetaData.LogID, AgentConfigVersionID: &session.RuntimeMetaData.AgentConfigVersionID, AgentConfigID: &session.RuntimeMetaData.AgentConfigID},
				LastMessageAt:      session.LastMessageAt.Format(time.RFC3339),
				CanResume:          &session.CanResume,
				CanNotResumeReason: canNotResumeReason,
				TemplateID:         &session.TemplateID,
				SourceSpaceID:      &session.SourceSpaceID,
				Scope:              session.Scope.ToIDL(),
				PermissionActions: lo.Map(session.PermissionActions, func(a entity.PermissionAction, _ int) nextagent.PermissionAction {
					return nextagent.PermissionAction(a)
				}),
				Starred: &session.Starred,
			}
		}),
		Total: total,
	})

}

func (h *Handler) ListSpaceSessions(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSpaceSessionsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list sessions request: %+v", util.ToJson(req))

	user, _ := h.AuthM.GetAccount(ctx, c)

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	if req.GetSpaceID() == "" {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
	}

	// 校验空间权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: req.SpaceID,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %+v", err)
		if errors.Is(err, permissionservice.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	space, err := h.SpaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get space: %+v", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get space")
		return
	}

	var listResult *sessionservice.ListSessionInSpaceResult

	var lastMessageBefore *time.Time
	if req.NextID != nil {
		timestamp, err := strconv.ParseInt(*req.NextID, 10, 64)
		if err == nil {
			lastMessageBefore = lo.ToPtr(time.UnixMilli(timestamp))
		}
	}
	var (
		createdAtStart *time.Time
		createdAtEnd   *time.Time
		updatedAtStart *time.Time
		updatedAtEnd   *time.Time
	)
	if req.CreatedTime != nil {
		if req.CreatedTime.StartTime != nil {
			createdAtStart = lo.ToPtr(time.UnixMilli(*req.CreatedTime.StartTime))
		}
		if req.CreatedTime.EndTime != nil {
			createdAtEnd = lo.ToPtr(time.UnixMilli(*req.CreatedTime.EndTime))
		}
	}
	if req.UpdatedTime != nil {
		if req.UpdatedTime.StartTime != nil {
			updatedAtStart = lo.ToPtr(time.UnixMilli(*req.UpdatedTime.StartTime))
		}
		if req.UpdatedTime.EndTime != nil {
			updatedAtEnd = lo.ToPtr(time.UnixMilli(*req.UpdatedTime.EndTime))
		}
	}

	switch space.Type {
	case entity.SpaceTypeProject:
		listResult, err = h.SessionService.ListSessionsInProjectSpace(ctx, sessionservice.ListSessionsInSpaceOption{
			SpaceID:           req.GetSpaceID(),
			User:              user,
			LastMessageBefore: lastMessageBefore,
			FilterType:        entity.SessionListFilterFromIDL(req.Type),
			Limit:             int(req.Limit),
			Search:            req.Search,
			Tab:               pack.ConvertSessionTabToEntity(req.Tab),
			Creators:          req.Creators,
			CreatedAtStart:    createdAtStart,
			CreatedAtEnd:      createdAtEnd,
			UpdatedAtStart:    updatedAtStart,
			UpdatedAtEnd:      updatedAtEnd,
			Statuses: lo.Map(req.Statuses, func(s nextagent.SessionStatus, _ int) entity.SessionStatus {
				return entity.SessionStatus(s)
			}),
			Scopes: lo.Map(req.Scopes, func(s nextagent.SessionScope, _ int) entity.SessionScope {
				return entity.SessionScope(s)
			}),
		})
	case entity.SpaceTypePersonal:
		listResult, err = h.SessionService.ListSessionsInPersonalSpace(ctx, sessionservice.ListSessionsInSpaceOption{
			SpaceID:           req.GetSpaceID(),
			User:              user,
			LastMessageBefore: lastMessageBefore,
			Limit:             int(req.Limit),
			Search:            req.Search,
			Tab:               pack.ConvertSessionTabToEntity(req.Tab),
			Creators:          req.Creators,
			CreatedAtStart:    createdAtStart,
			CreatedAtEnd:      createdAtEnd,
			UpdatedAtStart:    updatedAtStart,
			UpdatedAtEnd:      updatedAtEnd,
			Statuses: lo.Map(req.Statuses, func(s nextagent.SessionStatus, _ int) entity.SessionStatus {
				return entity.SessionStatus(s)
			}),
			Scopes: lo.Map(req.Scopes, func(s nextagent.SessionScope, _ int) entity.SessionScope {
				return entity.SessionScope(s)
			}),
		})
	default:
		err = errors.New("invalid space type")
	}

	if err != nil {
		log.V1.CtxError(ctx, "failed to list sessions: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list sessions")
		return
	}

	if listResult == nil {
		log.V1.CtxError(ctx, "failed to list sessions, listResult is nil")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list sessions")
		return
	}

	sessionFirstMsgMap, err := h.batchGetSessionUserFirstMsg(ctx, listResult.Sessions)
	if err != nil {
		// only log
		log.V1.CtxError(ctx, "failed to batch get session first msg: %+v", err)
	}

	var nextID *string
	if listResult.BeforeTime != nil {
		nextID = lo.ToPtr(strconv.FormatInt(listResult.BeforeTime.UnixMilli(), 10))
	}
	starredMap := make(map[string]bool)
	if len(listResult.Sessions) > 0 {
		sessionIDs, err := h.SessionService.ListStarSessionIDsByUsername(ctx, user.Username)
		if err != nil {
			log.V1.CtxError(ctx, "failed to list starred session ids: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "list session star error")
			return
		}
		for _, sessionID := range sessionIDs {
			starredMap[sessionID] = true
		}
	}

	c.JSON(http.StatusOK, nextagent.ListSpaceSessionsResponse{
		Sessions: lo.Map(listResult.Sessions, func(s *entity.Session, _ int) *nextagent.Session {
			s.Starred = starredMap[s.ID]
			return getSessionFromEntity(s, sessionFirstMsgMap)
		}),
		HasMore: lo.Ternary(listResult.BeforeTime == nil, false, true),
		NextID:  nextID,
		Count: &nextagent.ListSpaceSessionsCount{
			SpacePublic: listResult.SpacePublicCnt,
			SelfCreated: listResult.SelfCreatedCnt,
		},
	})
}

func (h *Handler) DeleteSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteSessionRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete session request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 校验权限
	if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		// SpaceID:   h.getSessionSpaceID(ctx, c),
		Action: entity.PermissionActionSessionDelete,
	}); !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			c.JSON(http.StatusOK, nextagent.DeleteSessionResponse{
				Message: "success",
			})
			return
		}
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}

	if user.Username != session.Creator {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	err = h.SessionService.DeleteSession(ctx, req.SessionID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete session : %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete session")
		return
	}

	// create session resource
	err = backoff.Retry(func() error {
		return h.PermissionService.DeleteResource(ctx, permissionservice.DeleteResourceOption{
			ResourceType:       lo.ToPtr(entity.ResourceTypeSession),
			ResourceExternalID: lo.ToPtr(session.ID),
		})
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 2))
	if err != nil {
		log.V1.CtxError(ctx, "failed to create resource: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.DeleteSessionResponse{
		Message: "success",
	})
}

func (h *Handler) BatchDeleteSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BatchDeleteSessionRequest](ctx, c)
	if req == nil || len(req.SessionIDs) == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session ids is empty")
		return
	}
	log.V1.CtxInfo(ctx, "delete session request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 并发校验权限
	var (
		wg                = sync.WaitGroup{}
		noPermissionCount int32
	)
	for _, sessionID := range req.SessionIDs {
		wg.Add(1)
		go func(sessionID string) {
			defer wg.Done()
			// 校验权限
			if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
				Account:   user,
				SessionID: lo.ToPtr(sessionID),
				Action:    entity.PermissionActionSessionDelete,
			}); !ok {
				atomic.AddInt32(&noPermissionCount, 1)
				return
			}
		}(sessionID)
	}
	wg.Wait()
	if noPermissionCount > 0 {
		return
	}

	err := h.SessionService.BatchDeleteSession(ctx, req.GetSessionIDs())
	if err != nil {
		log.V1.CtxError(ctx, "failed to batch delete session: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete session")
		return
	}

	c.JSON(http.StatusOK, nextagent.BatchDeleteSessionResponse{})
}

func (h *Handler) UpdateSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateSessionRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update session request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 校验权限
	if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		// SpaceID:   h.getSessionSpaceID(ctx, c),
		Action: entity.PermissionActionSessionUpdate,
	}); !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}

	// if user.Username != session.Creator {
	// 	hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
	// 	return
	// }
	if req.Status != nil {
		// 目前只支持更新为 Stopped 和 Canceled 状态
		if *req.Status != nextagent.SessionStatusStopped && *req.Status != nextagent.SessionStatusCanceled {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "update status is not stopped")
			return
		}

		// 原始状态已经是 Stopped 则不用再更新
		if session.Status == entity.SessionStatusStopped {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session already stopped")
			return
		}
	}

	session, err = h.SessionService.UpdateSession(ctx, sessionservice.UpdateSessionOption{
		SessionID:   req.SessionID,
		Title:       req.Title,
		Status:      entity.SessionStatusFromIDL(req.Status),
		OperateCube: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update session : %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update session")
		return
	}

	// 修改session的scope
	if req.Scope != nil {
		var (
			isSpacePublic bool
			scope         entity.SessionScope
		)
		switch *req.Scope {
		case nextagent.SessionScope_Private:
			isSpacePublic = false
			scope = entity.SessionScopePrivate
		case nextagent.SessionScope_ProjectPublic:
			isSpacePublic = true
			scope = entity.SessionScopeProjectPublic
		default:
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid scope")
			return
		}

		_, err := h.PermissionService.UpdateResource(ctx, permissionservice.UpdateResourceOptions{
			ResourceType:       lo.ToPtr(entity.ResourceTypeSession),
			ResourceExternalID: lo.ToPtr(session.ID),
			IsSpacePublic:      lo.ToPtr(isSpacePublic),
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to update resource: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		session.Scope = scope
	}

	c.JSON(http.StatusOK, nextagent.UpdateSessionResponse{
		Session: getSessionFromEntity(session, nil),
	})
}

func getMessageFromEntity(message *entity.Message) *nextagent.Message {
	if message == nil {
		return nil
	}
	return &nextagent.Message{
		MessageID: message.ID,
		SessionID: message.SessionID,
		Content:   message.Content.Content,
		Role:      string(message.Role),
		Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
			return &nextagent.Attachment{
				ID: a.ID,
			}
		}),
		Mentions:  pack.ConvertMentionsToDTO(message.Mentions),
		Creator:   message.Creator,
		CreatedAt: message.CreatedAt.Format(time.RFC3339),
		UpdatedAt: message.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *Handler) CheckCreateSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CheckCreateSessionRequest](ctx, c)
	if req == nil {
		return
	}
	rolesStr := strings.Split(req.Roles, ",")
	reqRoles := make([]nextagent.SessionRole, 0, len(rolesStr))
	for _, roleStr := range rolesStr {
		if roleStr == "" {
			continue
		}
		role, err := strconv.ParseInt(roleStr, 10, 64)
		if err != nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid role format")
			return
		}
		reqRoles = append(reqRoles, nextagent.SessionRole(role))
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	roles := make([]entity.SessionRole, 0, len(reqRoles))
	for _, role := range reqRoles {
		entityRole := entity.SessionRoleFromIDL(&role)
		if entityRole == nil || *entityRole == entity.SessionRoleUnknown {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid role")
			return
		}
		roles = append(roles, *entityRole)
	}

	result, err := h.SessionService.CanCreateSession(ctx, sessionservice.CanCreateSessionOption{
		Account: user,
		Roles:   roles,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check create session: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check create session")
		return
	}
	log.V1.CtxInfo(ctx, "check create session result: %v", util.ToJson(result))
	c.JSON(http.StatusOK, nextagent.CheckCreateSessionResponse{
		Allowed: result.Allowed, // 如果不在白名单，该值为 false
		Roles: lo.Map(result.Results, func(a entity.CreateSessionAllowed, _ int) *nextagent.SessionRoleAllowed {
			return &nextagent.SessionRoleAllowed{
				Role:           pointer.Get(a.Role.ToIDL()),
				Allowed:        a.Allowed,
				RemainingTimes: a.RemainingTimes,
			}
		}),
	})
	return
}

func getSessionFromEntity(session *entity.Session, sessionFirstMsgMap map[string]*entity.Message) *nextagent.Session {
	var (
		title    = session.Title
		firstMsg string
	)
	if sessionFirstMsgMap != nil {
		if msg, ok := sessionFirstMsgMap[session.ID]; ok {
			firstMsg = msg.Content.Content
		}
	}
	if title == "" { // 默认用首条消息作为标题
		title = firstMsg
	}

	canNotResumeReason := lo.ToPtr(session.CanNotResumeReason.ToIDL())
	if session.CanResume {
		canNotResumeReason = nil
	}
	return &nextagent.Session{
		ID:     session.ID,
		Status: getSessionStatus(session.Status),
		Title:  title,
		Context: &nextagent.SessionContext{
			UseInternalTool: session.Context.UseInternalTool,
			MCPs: gslice.FilterMap(session.Context.MCPs, func(m *entity.MCP) (*nextagent.MCP, bool) {
				return pack.ConvertMCPToDTOForUser(m, session.Creator), true
			}),
		},
		Role:               session.Role.ToIDL(),
		CreatedAt:          session.CreatedAt.Format(time.RFC3339),
		UpdatedAt:          session.UpdatedAt.Format(time.RFC3339),
		Creator:            &session.Creator,
		Metadata:           &nextagent.SessionRuntimeMetadata{LogID: &session.RuntimeMetaData.LogID, AgentConfigVersionID: &session.RuntimeMetaData.AgentConfigVersionID, AgentConfigID: &session.RuntimeMetaData.AgentConfigID},
		LastMessageAt:      session.LastMessageAt.Format(time.RFC3339),
		CanResume:          &session.CanResume,
		CanNotResumeReason: canNotResumeReason,
		TemplateID:         &session.TemplateID,
		SourceSpaceID:      &session.SourceSpaceID,
		Scope:              session.Scope.ToIDL(),
		PermissionActions: lo.Map(session.PermissionActions, func(a entity.PermissionAction, _ int) nextagent.PermissionAction {
			return nextagent.PermissionAction(a)
		}),
		Starred:        &session.Starred,
		FirstUserQuery: &firstMsg,
	}
}

func getSessionStatus(status entity.SessionStatus) nextagent.SessionStatus {
	var sessionStatus nextagent.SessionStatus
	if status == entity.SessionStatusBound {
		sessionStatus = nextagent.SessionStatusCreated
	} else {
		sessionStatus = agentservice.SessionStatus(status)
	}
	return sessionStatus
}

func (h *Handler) ListSessionPartial(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSessionPartialRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list session partial request: %+v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	partials, err := h.SessionService.ListRunningSessionPartials(ctx, sessionservice.ListRunningSessionStatusOption{Username: user.Username})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list session status: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "list session status error")
		return
	}
	sessions := make([]*nextagent.SessionPartial, 0, len(partials))
	for _, partial := range partials {
		sessions = append(sessions, &nextagent.SessionPartial{
			ID:     partial.ID,
			Status: getSessionStatus(partial.Status),
			Title:  partial.Title,
		})
	}
	c.JSON(http.StatusOK, nextagent.ListSessionPartialResponse{
		Sessions: sessions,
	})
}

func (h *Handler) GetSessionAgentRun(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetSessionAgentRunRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get session agent run request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID:             req.SessionID,
		NeedPermissionActions: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	// 用session状态来判断容器是否可用
	IsCubeAvailable := true
	if session.Status == entity.SessionStatusError ||
		session.Status == entity.SessionStatusStopped ||
		session.Status == entity.SessionStatusCreated ||
		session.Status == entity.SessionStatusPending {
		IsCubeAvailable = false
	}

	run, err := h.SessionService.GetSessionRuntimeMeta(ctx, req.SessionID, true)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session runtime meta: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session runtime meta")
		return
	}

	entity := &entity.AgentRuntimeMeta{
		RuntimeProvider: string(run.RuntimeMetadata.RuntimeProvider),
		ContainerID:     run.RuntimeMetadata.ContainerID,
		ContainerHost:   run.RuntimeMetadata.ContainerHost,
		WildcardDomain:  extractCubeWildcardDomain(run.RuntimeMetadata.ContainerHost),
	}

	c.JSON(http.StatusOK, nextagent.GetSessionAgentRunResponse{
		IsAvailable: IsCubeAvailable,
		RuntimeMeta: getRuntimeMetaFromEntity(entity),
		CreatedAt:   run.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   run.UpdatedAt.Format(time.RFC3339),
	})
}

type GetSpaceIDRequest struct {
	SpaceID *string `query:"space_id" form:"space_id" json:"space_id"`
}

func (h *Handler) RequireSpaceID() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		req := hertz.BindValidate[GetSpaceIDRequest](ctx, c)
		if req == nil {
			return
		}

		user, exist := h.AuthM.GetAccount(ctx, c)
		if !exist {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
			c.Abort()
			return
		}

		if req.SpaceID == nil || lo.FromPtr(req.SpaceID) == "" {
			spaceID, err := h.SpaceService.MustGetSpaceIDWithDefault(ctx, "", user.Username)
			if err != nil {
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get personal space")
				c.Abort()
				return
			}
			req.SpaceID = lo.ToPtr(spaceID)
		}
		c.Set(sessionSpaceIDKey, *req.SpaceID)
		c.Next(ctx)
	}
}

func (h *Handler) getSessionSpaceID(ctx context.Context, c *app.RequestContext) string {
	if val, ok := c.Get(sessionSpaceIDKey); ok {
		return val.(string)
	}
	return ""
}

type SessionPermissionCheckOption struct {
	Account   *authentity.Account
	SessionID *string
	SpaceID   *string
	Action    entity.PermissionAction
}

func (h *Handler) checkSessionPermission(ctx context.Context, c *app.RequestContext, opt SessionPermissionCheckOption) ([]entity.PermissionAction, bool) {
	if opt.Account == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return nil, false
	}

	checkOpt := permissionservice.CheckPermissionOption{
		Account: *opt.Account,
		GroupID: opt.SpaceID,
		Action:  opt.Action,
	}
	if opt.SessionID != nil {
		checkOpt.ResourceExternalID = opt.SessionID
		checkOpt.ResourceType = lo.ToPtr(entity.ResourceTypeSession)
	}
	permRet, err := h.PermissionService.CheckPermission(ctx, checkOpt)
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		if errors.Is(err, permissionservice.ErrResourceNotFound) || db.IsRecordNotFoundError(err) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
		} else {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		}
		return nil, false
	}
	if !permRet.Allowed {
		log.V1.CtxWarn(ctx, "permission denied, action: %v, spaceID: %v", opt.Action, opt.SpaceID)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return nil, false
	}
	permActions := make([]entity.PermissionAction, 0)
	if permRet.Resource != nil {
		for _, per := range permRet.Resource.Permissions {
			permActions = append(permActions, per.PermissionActions...)
		}
	}

	return lo.Uniq(permActions), true
}

func extractCubeWildcardDomain(host string) string {
	if host == "" {
		return ""
	}
	parts := strings.Split(host, ".")
	// 如果分割后的部分少于3个，返回空字符串
	if len(parts) < 3 {
		return ""
	}
	return parts[len(parts)-3]
}

func getRuntimeMetaFromEntity(runtimeMeta *entity.AgentRuntimeMeta) *nextagent.RuntimeMeta {
	if runtimeMeta == nil {
		return nil
	}
	return &nextagent.RuntimeMeta{
		Provider:       runtimeMeta.RuntimeProvider,
		ContainerID:    runtimeMeta.ContainerID,
		ContainerHost:  runtimeMeta.ContainerHost,
		WildcardDomain: runtimeMeta.WildcardDomain,
	}
}

func (h *Handler) isSessionShared(ctx context.Context, sessionID string) (bool, error) {
	replays, err := h.SessionService.ListSessionReplays(ctx, sessionservice.ListSessionReplaysOption{
		SessionID: sessionID,
		Limit:     1,
		Offset:    0,
	})
	if err != nil {
		return false, err
	}
	return len(replays) > 0, nil
}

func (h *Handler) batchGetSessionUserFirstMsg(ctx context.Context, sessions []*entity.Session) (map[string]*entity.Message, error) {
	batchMsgSessionID := []string{}
	for _, session := range sessions {
		batchMsgSessionID = append(batchMsgSessionID, session.ID)
	}
	sessionFirstMsgMap, err := h.SessionService.BatchGetFirstMessage(ctx, sessionservice.BatchGetMessagesOption{
		SessionIDs: batchMsgSessionID,
		Role:       entity.MessageRoleUser,
		Sync:       false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get batch msg sessions")
	}

	return sessionFirstMsgMap, nil
}

func (h *Handler) GetSessionMCPDetails(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetSessionMCPDetailsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	_, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionRead,
	})
	if !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		Account:   user,
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}

	larkOpenID := ""
	lu, err := h.LarkService.GetLarkUser(ctx, user.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = lu.OpenID
	}
	results := h.MCPService.BatchListMCPTools(ctx, &mcpservice.BatchMCPAccessContext{
		MCPs:           session.Context.MCPs,
		Token:          user.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
	})

	res := make([]*nextagent.SessionMCPDetail, 0)
	for i := range results {
		if results[i] == nil {
			continue
		}
		result := results[i]
		mcp := result.MCP
		if result.Skipped {
			continue
		}
		res = append(res, &nextagent.SessionMCPDetail{
			ID:           mcp.MCPID,
			Name:         mcp.Name,
			NameForAgent: mcp.NameForAgent,
			Description:  mcp.Description,
			Source:       nextagent.MCPSource(mcp.Source),
			Tools: gslice.Map(result.Tools, func(f *entity.MCPTool) *nextagent.MCPTool {
				return &nextagent.MCPTool{
					NameForAgent: f.Name,
					Name:         f.Name,
					Description:  f.Description,
				}
			}),
			ErrMessage: choose.IfLazyR(result.Err == nil, nil, func() *string {
				return gptr.Of(errors.Unwrap(err).Error())
			}),
		})
	}
	c.JSON(http.StatusOK, nextagent.GetSessionMCPDetailsResponse{
		MCPs: res,
	})
}
