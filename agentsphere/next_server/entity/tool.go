package entity

import agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"

type ToolCall struct {
	ID            string                     `json:"id"`
	Name          string                     `json:"name"`
	Content       string                     `json:"content"`
	Action        agententity.ToolCallAction `json:"action"`
	NeedKeepLogin bool                       `json:"need_keep_login"`
}
