package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

type ArtifactStatus string
type ArtifactType string
type ArtifactSource string
type ArtifactMetadata any

const (
	ArtifactTypeFile    ArtifactType = "file"
	ArtifactTypeCode    ArtifactType = "code"
	ArtifactTypeLink    ArtifactType = "link" // link 预期不会存储对应的文件，只会在 artifact 里面进行管理
	ArtifactTypeImage   ArtifactType = "image"
	ArtifactTypeProject ArtifactType = "project"
	ArtifactTypeOther   ArtifactType = "other" // 除了以上类型之外的类型，作为兜底

	// internal agent type
	ArtifactTypeLogs   ArtifactType = "logs" // an agent run should have only one logs artifact with key `logs`
	ArtifactTypeResult ArtifactType = "result"

	ArtifactStatusCreating  ArtifactStatus = "creating"
	ArtifactStatusDraft     ArtifactStatus = "draft"
	ArtifactStatusCompleted ArtifactStatus = "completed"

	ArtifactSourceUser  ArtifactSource = "user"
	ArtifactSourceAgent ArtifactSource = "agent"

	ArtifactKeyFileBaseline = "file-baseline"
	ArtifactKeyFileLarkMD   = "file-larkmd"
)

type Artifact struct {
	SessionID string         `json:"session_id"`
	ID        string         `json:"id"`
	Type      ArtifactType   `json:"type"` // 目前统一都是 file
	Status    ArtifactStatus `json:"status"`
	Source    ArtifactSource `json:"source"`
	Key       string         `json:"key"`
	Version   int32          `json:"version"`
	FileMetas FileMetas      `json:"file_metas"`
	CreatedAt time.Time      `json:"created_at"`
	// need to be casted to the following types, type MUST match ArtifactType
	Metadata ArtifactMetadata `json:"metadata"`
	Display  bool             `json:"display"`
}

type FileMeta struct {
	Name      string       `json:"name"`
	Size      int64        `json:"size"`
	Content   string       `json:"content,omitempty"` // Link 类型直接转 json 存在 content 里面
	MD5       string       `json:"md5,omitempty"`
	LarkToken string       `json:"lark_token,omitempty"` // 存在说明已经转换成飞书文档
	ImageXURI string       `json:"imagex_uri,omitempty"` // 图片类型会上传到 imagex 供前端调用
	Type      ArtifactType `json:"type,omitempty"`       // 目前一个 Artifact 可能会包含不同的业务类型的文件
	SubType   string       `json:"sub_type,omitempty"`   // 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（lark_doc/deployment）
	Patch     string       `json:"patch,omitempty"`      // patch 类型的文件，需要记录对应的 patch 内容
}

func (fm *FileMeta) ImageURL(domain, template string) string {
	if fm.ImageXURI == "" {
		return ""
	}
	return fmt.Sprintf("%s/%s~%s.image", domain, fm.ImageXURI, template)
}

// ResizeImageURL 拼接一个默认的缩放比例的图片链接，例如：http://p-boe.byted.org/tos-boe-i-jalkiib07l/5eafd4639d5f423d8da23890f4ced87d~tplv-jalkiib07l-2-v1:200:150.webp
func (fm *FileMeta) ResizeImageURL(domain, template string, width, height int) string {
	if fm.ImageXURI == "" {
		return ""
	}
	return fmt.Sprintf("%s/%s~%s:%d:%d.webp", domain, fm.ImageXURI, template, width, height)
}

type LinkContent struct {
	URL         string `json:"url,omitempty"`
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
}

type FileMetas []FileMeta

func (p FileMetas) Len() int           { return len(p) }
func (p FileMetas) Less(i, j int) bool { return p[i].Name < p[j].Name }
func (p FileMetas) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }

type FileArtifactTypeMetadata struct {
	Title string `json:"title"`
	Name  string `json:"name"`
	Size  int64  `json:"size"`
	Type  string `json:"type"`
}

type CodeArtifactTypeMetadata struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
	Type string `json:"type"`
} //

type ImageArtifactTypeMetadata struct {
	Name   string `json:"name"`
	Size   int64  `json:"size"`
	Type   string `json:"type"`
	Width  int64  `json:"width"`
	Height int64  `json:"height"`
}

type LinkArtifactTypeMetadata struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
	Type string `json:"type"`
}

type ProjectArtifactTypeMetadata struct {
	Name   string `json:"name"`
	Path   string `json:"path"`
	Commit string `json:"commit"`
	Size   int64  `json:"size"`
	Type   string `json:"type"`
}

func (p ProjectArtifactTypeMetadata) ToMap() map[string]any {
	return map[string]any{
		"name":   p.Name,
		"path":   p.Path,
		"commit": p.Commit,
	}
}

// ResultArtifactMetadata stores arbitrary data created by the agent
// it should keep stable for specific agent and should not be available to the user
type ResultArtifactMetadata map[string]any

type LogsArtifactMetadata struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
	Type string `json:"type"`
}

func UnmarshalMetadata(typ ArtifactType, data []byte) (metadata ArtifactMetadata, err error) {
	if len(data) == 0 {
		return nil, nil
	}

	switch typ {
	case ArtifactTypeFile:
		var res FileArtifactTypeMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeCode:
		var res CodeArtifactTypeMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeLink:
		var res LinkArtifactTypeMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeImage:
		var res ImageArtifactTypeMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeLogs:
		var res LogsArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeResult:
		var res ResultArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeProject:
		var res ProjectArtifactTypeMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	}
	if err != nil {
		return nil, err
	}
	return metadata, nil
}

type LinkArtifactKeySource string

func (s LinkArtifactKeySource) String() string {
	return string(s)
}

const (
	LinkArtifactKeySourceDeployment LinkArtifactKeySource = "deployment"
	LinkArtifactKeySourceLarkDoc    LinkArtifactKeySource = "lark_doc"
	LinkArtifactKeySourceLarkSheet  LinkArtifactKeySource = "lark_sheet"
	LinkArtifactKeySourceURL        LinkArtifactKeySource = "url" // 通用的链接类型，前端点击直接跳转，不展示预览
)
