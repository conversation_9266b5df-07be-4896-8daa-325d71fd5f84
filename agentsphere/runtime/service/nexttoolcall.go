package runtimeservice

import (
	"context"
	"runtime/debug"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

const (
	ToolCallRequiredNameAskUser     = "ask_user"
	ToolCallRequiredNameTakeControl = "ask_take"
)

func (s *Service) packToolCallRequired(ctx context.Context, event entity.SessionDataStreamEvent,
	isOffline bool, sessionID, username string) *nextidl.ToolCallRequiredEvent {
	if event.NextToolCallRequired == nil {
		return nil
	}
	if isOffline {
		// 更新 session 状态为 waiting
		session, err := s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
			ID:     sessionID,
			Status: lo.ToPtr(nextentity.SessionStatusWaiting),
		})
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to update session %s: %v", sessionID, err)
		}

		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, string(debug.Stack()))
				}
			}()

			// 发送 Lark 通知
			title := "你的任务"
			if session != nil {
				title = session.Title
			}
			switch event.NextToolCallRequired.Tool {
			case ToolCallRequiredNameTakeControl:
				err = s.nextLarkService.SendTaskNeedTakeBrowserNotification(ctx, sessionID, username, title)
			default:
				err = s.nextLarkService.SendTaskNeedHumanNotification(ctx, sessionID, username, title)
			}
			if err != nil {
				log.V1.CtxError(ctx, "failed to send need human lark notification: %v", err)
			}
		}()
	}
	planStepID, err := s.GetPlanStepIDByAgentStepID(ctx, event.NextToolCallRequired.StepID)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get plan step id by agent step id: %v", err)
	}
	e := &nextidl.ToolCallRequiredEvent{
		EventID:           s.idGen.NewID(),
		SessionID:         sessionID,
		Tool:              event.NextToolCallRequired.Tool,
		Question:          "",
		Type:              "",
		Timestamp:         event.Timestamp.Unix(),
		EventOffset:       event.EventOffset,
		ToolCallID:        event.NextToolCallRequired.ID,
		EventKey:          nextentity.GetToolCallRequiredEventKey(sessionID, event.NextToolCallRequired.ID, event.EventOffset),
		TakeBrowserParams: nil,
		StepID:            planStepID,
	}
	switch event.NextToolCallRequired.Tool {
	case ToolCallRequiredNameAskUser:
		e.Question = conv.DefaultAny[string](event.NextToolCallRequired.Parameters["message"])
		e.Type = nextidl.ToolCallRequiredTypeAsk
	case ToolCallRequiredNameTakeControl:
		e.TakeBrowserParams = &nextidl.TakeBrowserParams{
			StreamURL:    conv.DefaultAny[string](event.NextToolCallRequired.Parameters["stream_url"]),
			Reason:       conv.DefaultAny[string](event.NextToolCallRequired.Parameters["take_reason"]),
			Timeout:      int64(conv.DefaultAny[float64](event.NextToolCallRequired.Parameters["timeout"])),
			AskKeepLogin: lo.ToPtr(conv.DefaultAny[bool](event.NextToolCallRequired.Parameters["ask_save_cookies"])),
		}
		e.Type = nextidl.ToolCallRequiredTypeTakeControl
	}
	return e
}

func (s *Service) packToolCallConfirmed(ctx context.Context, event entity.SessionDataStreamEvent,
	sessionID string) *nextidl.ToolCallConfirmedEvent {
	if event.NextToolCallConfirmed == nil {
		return nil
	}
	var parameters toolCallConfirmParameters
	err := mapstructure.Decode(event.NextToolCallConfirmed.Parameters, &parameters)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to decode tool call confirmed parameters: %v", err)
	}
	var screenshotURL string
	if parameters.Screenshot.ArtifactID != "" {
		screenshotURL = s.getScreenshotURL(ctx, parameters.Screenshot.ArtifactID, parameters.Screenshot.Path)
	}
	planStepID, err := s.GetPlanStepIDByAgentStepID(ctx, event.NextToolCallConfirmed.StepID)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get plan step id by agent step id: %v", err)
	}
	return &nextidl.ToolCallConfirmedEvent{
		EventID:     s.idGen.NewID(),
		SessionID:   sessionID,
		ToolCallID:  event.NextToolCallConfirmed.ToolCallID,
		StepID:      planStepID,
		Action:      pack.ConvertToolCallActionToDTO(event.NextToolCallConfirmed.Action),
		Reason:      event.NextToolCallConfirmed.Reason,
		Timestamp:   event.Timestamp.Unix(),
		EventOffset: event.EventOffset,
		EventKey:    nextentity.GetToolCallConfirmedEventKey(sessionID, event.NextToolCallConfirmed.ToolCallID, event.EventOffset),
		Browser: &nextidl.Browser{
			ScreenshotURL: screenshotURL,
			URL:           parameters.BrowserState.URL,
			ContentType:   nextidl.BrowserContentType_BrowserTypeScreenshot,
		},
	}
}

type toolCallConfirmParameters struct {
	BrowserState struct {
		Title string `mapstructure:"title"`
		URL   string `mapstructure:"url"`
	} `mapstructure:"browser_state"`
	Screenshot nextidl.Screenshot `mapstructure:"screenshot_without_highlights"`
}
