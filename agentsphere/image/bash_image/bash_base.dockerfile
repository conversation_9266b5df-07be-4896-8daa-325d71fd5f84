FROM dockerhub-internal.byted.org/base/debian.bookworm.python311

ARG TARGETARCH
ARG REGION

ENV PATH="$PATH:/root/.local/bin" \
    HTTP_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    HTTPS_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    NO_PROXY=.byted.org

# libc:amd64 for M1 mac to test agent in local container
RUN dpkg --add-architecture amd64 && \
  apt update && \
  apt install -y\
    build-essential \
    libc6:amd64 \
    jq \
    sshpass \
    curl \
    wget \
    rsync \
    net-tools \
    xz-utils \
    zip \
    unzip \
    tar \
    p7zip-full \
    ripgrep \
    bvc \
    git \
    file \
    # fonts
    fonts-freefont-ttf \
    fonts-ipafont-gothic \
    fonts-wqy-zenhei \
    fonts-thai-tlwg \
    fonts-kacst \
    fonts-symbola \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    graphviz \
    pandoc && \
    # extra fonts for pdf generation, Source Han Sans (adobe) = Noto Sans CJK (google)
    mkdir -p /usr/share/fonts/truetype/source-han-sans && curl -LsSf https://github.com/adobe-fonts/source-han-sans/raw/release/Variable/TTF/SourceHanSansSC-VF.ttf -o /usr/share/fonts/truetype/source-han-sans/SourceHanSansSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype/source-han-serif && curl -LsSf https://github.com/adobe-fonts/source-han-serif/raw/release/Variable/TTF/SourceHanSerifSC-VF.ttf -o /usr/share/fonts/truetype/source-han-serif/SourceHanSerifSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype && curl -LsSf https://github.com/StellarCN/scp_zh/raw/refs/heads/master/fonts/SimHei.ttf -o /usr/share/fonts/truetype/SimHei.ttf && \
    # Noto CJK SC for matplotlib
    mkdir -p /usr/share/fonts/opentype/noto-cjk-sc && curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Serif/OTF/SimplifiedChinese/NotoSerifCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSerifCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Serif/OTF/SimplifiedChinese/NotoSerifCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSerifCJKsc-Bold.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansCJKsc-Bold.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/Mono/NotoSansMonoCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansMonoCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/Mono/NotoSansMonoCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansMonoCJKsc-Bold.otf

# install code-server 4.100.3
# ~400MiB
RUN apt install -y sshpass jq gettext-base supervisor && \
    if [ "$REGION" != "china-north-lf" ]; then curl -v https://tosv.byted.org/obj/bitscloud-sg/aime/code-server/code-server-4.100.3-linux-amd64.tar.gz -o code-server-4.100.3-linux-amd64.tar.gz; else curl -v https://tosv.byted.org/obj/dolphin-inner/cusk/aime/code-server/code-server-4.100.3-linux-amd64.tar.gz -o code-server-4.100.3-linux-amd64.tar.gz; fi && \
    tar xvf code-server-4.100.3-linux-amd64.tar.gz && \
    mv code-server-4.100.3-linux-amd64 /usr/lib/code-server && \
    rm -rf code-server-4.100.3-linux-amd64.tar.gz && \
    HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone stratos/cube/code_server /home/<USER>/.config && \
    echo '#!/usr/bin/env sh' > /usr/bin/code-server && \
    echo 'exec /usr/lib/code-server/bin/code-server "$@"' > /usr/bin/code-server && \
    chmod a+x /usr/bin/code-server && \
    echo '{}' > /home/<USER>/.config/code-server/vscode/coder.json && \
    rm -rf /home/<USER>/.config/code-server/vscode/logs/* && \
    rm -rf /home/<USER>/.config/code-server/*.log

# cube-rbs
RUN HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone -f stratos/cube/rbs /usr/local/bin/cube-rbs

# install uv, python 3.11
# ~870MiB
RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
  uv venv --python 3.11 && \
  uv pip install --upgrade pip && \
    # data analysis toolkits; install to default python as llms are tend to use default interpreter
    pip install \
      pandas \
      numpy \
      matplotlib==3.10.5 \
      pillow \
      folium \
      requests \
      beautifulsoup4 \
      geopy \
      jinja2 \
      seaborn \
      plotly \
      graphviz \
      pydot \
      statsmodels \
      openpyxl \
      uvicorn==0.22.0 \
      # https://github.com/encode/httpx/issues/3221
      httpx==0.23.3 \
      fastapi==0.67.0 && \
      # 修改 matplotlib 类库中所有 .mplstyle 文件，删除所有 font.family 和 font.sans-serif 相关的行
      find /usr/local/lib/python3.11/site-packages/matplotlib/mpl-data/stylelib -type f -name "*.mplstyle" -exec sh -c 'for file; do cp "$file" "$file.bak"; grep -Ev "^\s*(font\.family|font\.sans-serif)\s*:" "$file" > "${file}.tmp" && mv -f "${file}.tmp" "$file"; done' _ {} +

# 修复 matplotlib 字体问题，强制设置 rcParams 中的 font 配置防止被覆盖
COPY agentsphere/lib/matplotlib/__init__.py /usr/local/lib/python3.11/site-packages/matplotlib/__init__.py

# set default font for matplotlib
RUN echo 'export MPLCONFIGDIR=/root/.matplotlib' >> /root/.bashrc \
    && mkdir -p /root/.matplotlib \
    && echo "font.family: sans-serif" > /root/.matplotlib/matplotlibrc \
    && echo "font.sans-serif: Noto Sans CJK SC" >> /root/.matplotlib/matplotlibrc

# git config
COPY agentsphere/image/gitconfig /etc/gitconfig

# lldb for CoredumpAi: https://bytedance.larkoffice.com/docx/WIBTdy9o0ogYyLxzgAPcaXTPnSg
ENV PYTHONPATH="/usr/lib/llvm-16/lib/python3.11/site-packages:/usr/lib/llvm-16/lib/python3/dist-packages/lldb"
RUN apt update && apt install -y lldb-16 && \
    ln -s /usr/bin/lldb-16 /usr/bin/lldb && \
    echo 'export PYTHONPATH=/usr/lib/llvm-16/lib/python3.11/site-packages:/usr/lib/llvm-16/lib/python3/dist-packages/lldb' >> /etc/profile

RUN if [ "$REGION" != "" ] && [ "$REGION" != "china-north-lf" ]; then apt-get update && DEBIAN_FRONTEND=noninteractive apt-get  install tzdata && rm -rf /etc/localtime && /bin/cp -f /usr/share/zoneinfo/Etc/UTC /etc/localtime && echo "Etc/UTC" > /etc/timezone; fi;

# bash profile
ENV BASH_ENV="/root/.bash_profile"
COPY agentsphere/image/bash_profile $BASH_ENV

ENV HTTPS_PROXY= \
    HTTP_PROXY= \
    NO_PROXY=

CMD ["/home/<USER>/.config/code-server/start.sh"]
