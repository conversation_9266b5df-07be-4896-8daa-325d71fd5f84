#!/bin/bash
set -e

if [[ -z "$1" ]]; then
  echo "Usage: ./build.sh <module_name>" && exit 1;
fi

echo "🚀 building $1..."
# imageproxy 不依赖conf, 直接编译
if [[ $1 = "imageproxy" ]]; then
  make build_imageproxy
  echo "✅ build $1 completed"
  exit 0
fi
mkdir -p output/conf output/resources
find conf/config.$1.* -type f ! -name "*_local.*" | xargs -I{} cp {} output/conf/
cp -r resources/link output/resources/
cp -r resources/tokenizer output/resources/

if [[ $1 = "devgpt" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_devgpt_server
    make build_context_server
elif [[ $1 = "copilot" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_copilot_server
elif [[ $1 = "ide" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_ide_server
elif [[ $1 = "ide_cronjob" ]]; then
  cp conf/hertz.config.yaml output/conf/
  make build_ide_cronjob
elif [[ $1 = "loganalysis" ]]; then
  if [[ $2 = "consumer" ]]; then
    make build_log_analysis_consumer_server
  else
    cp conf/hertz.config.yaml output/conf/
    make build_log_analysis_server
  fi
elif [[ $1 = "knowledgebase" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_knowledgebase
elif [[ $1 = "memory" ]]; then
    mkdir -p output/prompt/
    cp conf/hertz.config.yaml output/conf/
    cp memory/service/chat/prompt/*.yaml output/prompt/
    make build_memory
elif [[ $1 = "memory_faas_tcc" ]]; then
    cp memory_faas/tcc_faas/bytefaas.yml output/
    make build_memory_faas_tcc
elif [[ $1 = "memory_faas_scm" ]]; then
    cp memory_faas/scm_faas/bytefaas.yml output/
    make build_memory_faas_scm
elif [[ $1 = "splitter" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_splitter
elif [[ $1 = "search" ]]; then
    cp conf/hertz.config.yaml output/conf/
    cp scripts/run_search.sh output/
    make build_search_server
elif [[ $1 = "llminfra" ]]; then
  cp conf/*llminfra*.yaml output/conf/
  make build_llminfra_server
elif [[ $1 = "knowledge" ]]; then
  cp conf/hertz.config.yaml output/conf/
  make build_devai_knowledge
elif [[ $1 = "flowoperator" ]]; then
  cp conf/hertz.config.yaml output/conf/
  make build_flowoperator
elif [[ $1 = "mentor" ]]; then
  if [[ $2 = "http" ]]; then
    cp conf/hertz.config.yaml output/conf/
    mkdir -p "output/agent_config"
    mkdir -p "output/landing_scripts"
    cp mentor/service/training/agent/agent_config/*.yaml output/agent_config/
    cp mentor/service/training/agent/landing_scripts/*.yaml output/landing_scripts/
    make build_mentor_http
  else
    make build_mentor_cronjob
  fi
elif [[ $1 = "devai_datasource_faas" ]]; then
  cp conf/config.knowledge.*.yaml output/conf/
  mkdir -p output/resources
  cp devai/data/service/loader/resources/* output/resources/
  make build_devai_datasource_faas
elif [[ $1 = "devai_datasource_cronjob" ]]; then
  cp conf/config.knowledge.*.yaml output/conf/
  make build_devai_datasource_cronjob
elif [[ $1 = "devai_tos2hdfs_cronjob" ]]; then
  cp conf/config.knowledge.*.yaml output/conf/
  cp scripts/ufs* output/
  chmod +x output/ufs*
  make build_devai_tos2hdfs_cronjob
elif [[ $1 = "knowledgeview_dsyncer_faas" ]]; then
  cp conf/config.knowledge.*.yaml output/conf/
  make build_devai_knowledgeview_dsyncer_faas
elif [[ $1 = "devgpt2devai_dsyncer_faas" ]]; then
  cp conf/config.knowledge.*.yaml output/conf/
  make build_devgpt2devai_dsyncer_faas
elif [[ $1 = "assistant" ]]; then
   cp conf/config.assistant.*.yaml output/conf/
    make build_devai_assistant
elif [[ $1 = "bits_copilot_agent" ]]; then
  cp conf/config.bits.agent.*.yaml output/conf/
  cp conf/hertz.config.yaml output/conf/
  make build_bits_copilot_agent
  bash -c "$(curl -fsL https://tosv.byted.org/obj/uitesting/tos_upload_blame.sh)" || echo ""
elif [[ $1 = "agentsphere_apiserver" ]]; then
  cp conf/config.agentsphere.apiserver.*.yaml output/conf/
  cp conf/hertz.config.yaml output/conf/
  make build_agentsphere_apiserver
elif [[ $1 = "agentsphere_nextserver" ]]; then
    cp conf/config.agentsphere.nextserver.*.yaml output/conf/
    cp conf/hertz.config.yaml output/conf/
    make build_agentsphere_nextserver
elif [[ $1 = "agentsphere_runtimeserver" ]]; then
    cp conf/hertz.config.yaml output/conf/
    make build_agentsphere_runtimeserver
elif [[ $1 = "agentsphere_runtime" ]]; then
  export GOOS=linux GOARCH=amd64
  echo -e "building agentsphere runtime for \\033[0;31mLinux/amd64\\033[0m, to build MacOS version, use \\033[0;32mbuild.sh agentsphere_runtime_local\\033[0m"
  make build_agentsphere_runtime
elif [[ $1 = "agentsphere_runtime_local" ]]; then
  make build_agentsphere_runtime
elif [[ $1 = "render" ]]; then
  make build_render_server
elif [[ $1 = "agentsphere_relay" ]]; then
  cp conf/config.agentsphere.relay.*.yaml output/conf/
  cp conf/hertz.config.yaml output/conf/
  make build_agentsphere_relay
elif [[ $1 = "idecopilot_entitlement_consumer" ]]; then
  cp conf/config.ide.*.yaml output/conf/
  make build_idecopilot_entitlement_consumer
elif [[ $1 = "codeai" ]]; then
  cp conf/hertz.config.yaml output/conf/
  cp conf/*codeai*.yaml output/conf/
  make build_codeai_server
elif [[ $1 = "agentsphere_a2a_router" ]]; then
  cp conf/config.agentsphere.a2a_router.*.yaml output/conf/
  cp conf/hertz.config.yaml output/conf/
  make build_agentsphere_a2a_router
else
    echo "❗ invalid module name: $1" && exit 1
fi

echo "✅ build $1 completed"
