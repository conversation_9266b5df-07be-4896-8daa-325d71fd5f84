// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	ProcessStatusSuccess = "success"

	ProcessStatusFailed = "failed"

	ProcessStatusProcessing = "processing"

	OperateTypeAdd = "add"

	OperateTypeDelete = "delete"
)

type ControlPlane int64

const (
	// UNSPECIFIED 在阶段模板配置里，可以额外用于未指定控制面（自由流水线）
	ControlPlane_CONTROL_PLANE_UNSPECIFIED ControlPlane = 0
	ControlPlane_CONTROL_PLANE_CN          ControlPlane = 1
	ControlPlane_CONTROL_PLANE_I18N        ControlPlane = 2
	// deprecated 沿用 ByteCycle US-TTP 方案使用的枚举值
	ControlPlane_CONTROL_PLANE_TTP    ControlPlane = 3
	ControlPlane_CONTROL_PLANE_EU_TTP ControlPlane = 4
	ControlPlane_CONTROL_PLANE_US_TTP ControlPlane = 5
	// I18N non TT
	ControlPlane_CONTROL_PLANE_I18N_BD ControlPlane = 6
)

func (p ControlPlane) String() string {
	switch p {
	case ControlPlane_CONTROL_PLANE_UNSPECIFIED:
		return "CONTROL_PLANE_UNSPECIFIED"
	case ControlPlane_CONTROL_PLANE_CN:
		return "CONTROL_PLANE_CN"
	case ControlPlane_CONTROL_PLANE_I18N:
		return "CONTROL_PLANE_I18N"
	case ControlPlane_CONTROL_PLANE_TTP:
		return "CONTROL_PLANE_TTP"
	case ControlPlane_CONTROL_PLANE_EU_TTP:
		return "CONTROL_PLANE_EU_TTP"
	case ControlPlane_CONTROL_PLANE_US_TTP:
		return "CONTROL_PLANE_US_TTP"
	case ControlPlane_CONTROL_PLANE_I18N_BD:
		return "CONTROL_PLANE_I18N_BD"
	}
	return "<UNSET>"
}

func ControlPlaneFromString(s string) (ControlPlane, error) {
	switch s {
	case "CONTROL_PLANE_UNSPECIFIED":
		return ControlPlane_CONTROL_PLANE_UNSPECIFIED, nil
	case "CONTROL_PLANE_CN":
		return ControlPlane_CONTROL_PLANE_CN, nil
	case "CONTROL_PLANE_I18N":
		return ControlPlane_CONTROL_PLANE_I18N, nil
	case "CONTROL_PLANE_TTP":
		return ControlPlane_CONTROL_PLANE_TTP, nil
	case "CONTROL_PLANE_EU_TTP":
		return ControlPlane_CONTROL_PLANE_EU_TTP, nil
	case "CONTROL_PLANE_US_TTP":
		return ControlPlane_CONTROL_PLANE_US_TTP, nil
	case "CONTROL_PLANE_I18N_BD":
		return ControlPlane_CONTROL_PLANE_I18N_BD, nil
	}
	return ControlPlane(0), fmt.Errorf("not a valid ControlPlane string")
}

func ControlPlanePtr(v ControlPlane) *ControlPlane { return &v }
func (p *ControlPlane) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ControlPlane(result.Int64)
	return
}

func (p *ControlPlane) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ServiceType int64

const (
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// TCE
	ServiceType_SERVICE_TYPE_TCE ServiceType = 1
	// ByteFaaS
	ServiceType_SERVICE_TYPE_FAAS ServiceType = 2
	// Cronjob
	ServiceType_SERVICE_TYPE_CRONJOB ServiceType = 3
	// 新版 Web
	ServiceType_SERVICE_TYPE_WEB ServiceType = 4
	// 跨端
	ServiceType_SERVICE_TYPE_HYBRID ServiceType = 5
	// 组件/模块
	ServiceType_SERVICE_TYPE_LIBRARY ServiceType = 6
	// 新版 NodeJS
	ServiceType_SERVICE_TYPE_NODEJS ServiceType = 7
	// 新版 Monorepo
	ServiceType_SERVICE_TYPE_MONOREPO ServiceType = 8
	// sidecar
	ServiceType_SERVICE_TYPE_SIDECAR ServiceType = 9
	// webapp
	ServiceType_SERVICE_TYPE_WEB_APP ServiceType = 10
	// 旧版web
	ServiceType_SERVICE_TYPE_OLD_WEB ServiceType = 11
	// TCC
	ServiceType_SERVICE_TYPE_TCC ServiceType = 12
	// 自定义项目类型
	ServiceType_SERVICE_TYPE_CUSTOM ServiceType = 100
)

func (p ServiceType) String() string {
	switch p {
	case ServiceType_SERVICE_TYPE_UNSPECIFIED:
		return "SERVICE_TYPE_UNSPECIFIED"
	case ServiceType_SERVICE_TYPE_TCE:
		return "SERVICE_TYPE_TCE"
	case ServiceType_SERVICE_TYPE_FAAS:
		return "SERVICE_TYPE_FAAS"
	case ServiceType_SERVICE_TYPE_CRONJOB:
		return "SERVICE_TYPE_CRONJOB"
	case ServiceType_SERVICE_TYPE_WEB:
		return "SERVICE_TYPE_WEB"
	case ServiceType_SERVICE_TYPE_HYBRID:
		return "SERVICE_TYPE_HYBRID"
	case ServiceType_SERVICE_TYPE_LIBRARY:
		return "SERVICE_TYPE_LIBRARY"
	case ServiceType_SERVICE_TYPE_NODEJS:
		return "SERVICE_TYPE_NODEJS"
	case ServiceType_SERVICE_TYPE_MONOREPO:
		return "SERVICE_TYPE_MONOREPO"
	case ServiceType_SERVICE_TYPE_SIDECAR:
		return "SERVICE_TYPE_SIDECAR"
	case ServiceType_SERVICE_TYPE_WEB_APP:
		return "SERVICE_TYPE_WEB_APP"
	case ServiceType_SERVICE_TYPE_OLD_WEB:
		return "SERVICE_TYPE_OLD_WEB"
	case ServiceType_SERVICE_TYPE_TCC:
		return "SERVICE_TYPE_TCC"
	case ServiceType_SERVICE_TYPE_CUSTOM:
		return "SERVICE_TYPE_CUSTOM"
	}
	return "<UNSET>"
}

func ServiceTypeFromString(s string) (ServiceType, error) {
	switch s {
	case "SERVICE_TYPE_UNSPECIFIED":
		return ServiceType_SERVICE_TYPE_UNSPECIFIED, nil
	case "SERVICE_TYPE_TCE":
		return ServiceType_SERVICE_TYPE_TCE, nil
	case "SERVICE_TYPE_FAAS":
		return ServiceType_SERVICE_TYPE_FAAS, nil
	case "SERVICE_TYPE_CRONJOB":
		return ServiceType_SERVICE_TYPE_CRONJOB, nil
	case "SERVICE_TYPE_WEB":
		return ServiceType_SERVICE_TYPE_WEB, nil
	case "SERVICE_TYPE_HYBRID":
		return ServiceType_SERVICE_TYPE_HYBRID, nil
	case "SERVICE_TYPE_LIBRARY":
		return ServiceType_SERVICE_TYPE_LIBRARY, nil
	case "SERVICE_TYPE_NODEJS":
		return ServiceType_SERVICE_TYPE_NODEJS, nil
	case "SERVICE_TYPE_MONOREPO":
		return ServiceType_SERVICE_TYPE_MONOREPO, nil
	case "SERVICE_TYPE_SIDECAR":
		return ServiceType_SERVICE_TYPE_SIDECAR, nil
	case "SERVICE_TYPE_WEB_APP":
		return ServiceType_SERVICE_TYPE_WEB_APP, nil
	case "SERVICE_TYPE_OLD_WEB":
		return ServiceType_SERVICE_TYPE_OLD_WEB, nil
	case "SERVICE_TYPE_TCC":
		return ServiceType_SERVICE_TYPE_TCC, nil
	case "SERVICE_TYPE_CUSTOM":
		return ServiceType_SERVICE_TYPE_CUSTOM, nil
	}
	return ServiceType(0), fmt.Errorf("not a valid ServiceType string")
}

func ServiceTypePtr(v ServiceType) *ServiceType { return &v }
func (p *ServiceType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ServiceType(result.Int64)
	return
}

func (p *ServiceType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProcessStatus = string

type OperateType = string

type CodeRepo struct {
	RepoID        string        `thrift:"RepoID,1,required" json:"repo_id"`
	RepoName      string        `thrift:"RepoName,2,required" json:"repo_name"`
	IsUploaded    bool          `thrift:"IsUploaded,3" json:"is_uploaded"`
	Desc          *string       `thrift:"Desc,4,optional" json:"desc"`
	AvatarUrl     *string       `thrift:"AvatarUrl,5,optional" json:"avatar_url"`
	Url           string        `thrift:"Url,6,required" json:"url"`
	ProcessStatus ProcessStatus `thrift:"ProcessStatus,7" json:"process_status"`
}

func NewCodeRepo() *CodeRepo {
	return &CodeRepo{}
}

func (p *CodeRepo) InitDefault() {
}

func (p *CodeRepo) GetRepoID() (v string) {
	return p.RepoID
}

func (p *CodeRepo) GetRepoName() (v string) {
	return p.RepoName
}

func (p *CodeRepo) GetIsUploaded() (v bool) {
	return p.IsUploaded
}

var CodeRepo_Desc_DEFAULT string

func (p *CodeRepo) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return CodeRepo_Desc_DEFAULT
	}
	return *p.Desc
}

var CodeRepo_AvatarUrl_DEFAULT string

func (p *CodeRepo) GetAvatarUrl() (v string) {
	if !p.IsSetAvatarUrl() {
		return CodeRepo_AvatarUrl_DEFAULT
	}
	return *p.AvatarUrl
}

func (p *CodeRepo) GetUrl() (v string) {
	return p.Url
}

func (p *CodeRepo) GetProcessStatus() (v ProcessStatus) {
	return p.ProcessStatus
}

func (p *CodeRepo) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *CodeRepo) IsSetAvatarUrl() bool {
	return p.AvatarUrl != nil
}

func (p *CodeRepo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeRepo(%+v)", *p)
}

type Service struct {
	// 项目唯一标识
	UniqueId string `thrift:"UniqueId,1,required" json:"unique_id"`
	// 项目类型
	Type ServiceType `thrift:"Type,2,required" json:"type"`
	// 项目名称
	Name string `thrift:"Name,3,required" json:"name"`
	// 控制面
	ControlPlane ControlPlane `thrift:"ControlPlane,4,required" json:"control_plane"`
	// 是否上传
	IsUploaded    bool          `thrift:"IsUploaded,5" json:"is_uploaded"`
	ProcessStatus ProcessStatus `thrift:"ProcessStatus,6" json:"process_status"`
	Url           string        `thrift:"Url,7,required" json:"url"`
}

func NewService() *Service {
	return &Service{}
}

func (p *Service) InitDefault() {
}

func (p *Service) GetUniqueId() (v string) {
	return p.UniqueId
}

func (p *Service) GetType() (v ServiceType) {
	return p.Type
}

func (p *Service) GetName() (v string) {
	return p.Name
}

func (p *Service) GetControlPlane() (v ControlPlane) {
	return p.ControlPlane
}

func (p *Service) GetIsUploaded() (v bool) {
	return p.IsUploaded
}

func (p *Service) GetProcessStatus() (v ProcessStatus) {
	return p.ProcessStatus
}

func (p *Service) GetUrl() (v string) {
	return p.Url
}

func (p *Service) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Service(%+v)", *p)
}

type PlatformConfig struct {
	MeegoSpaceList []*MeegoSpace `thrift:"MeegoSpaceList,1,optional" json:"meego_space_list"`
}

func NewPlatformConfig() *PlatformConfig {
	return &PlatformConfig{}
}

func (p *PlatformConfig) InitDefault() {
}

var PlatformConfig_MeegoSpaceList_DEFAULT []*MeegoSpace

func (p *PlatformConfig) GetMeegoSpaceList() (v []*MeegoSpace) {
	if !p.IsSetMeegoSpaceList() {
		return PlatformConfig_MeegoSpaceList_DEFAULT
	}
	return p.MeegoSpaceList
}

func (p *PlatformConfig) IsSetMeegoSpaceList() bool {
	return p.MeegoSpaceList != nil
}

func (p *PlatformConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConfig(%+v)", *p)
}

type LarkDocConfig struct {
	// 单篇文档信息
	SingleDocs []string `thrift:"SingleDocs,1,optional" json:"single_docs"`
	// 导入的知识库列表
	WikiList []*WikiDocConfig `thrift:"WikiList,2,optional" json:"wiki_list"`
}

func NewLarkDocConfig() *LarkDocConfig {
	return &LarkDocConfig{}
}

func (p *LarkDocConfig) InitDefault() {
}

var LarkDocConfig_SingleDocs_DEFAULT []string

func (p *LarkDocConfig) GetSingleDocs() (v []string) {
	if !p.IsSetSingleDocs() {
		return LarkDocConfig_SingleDocs_DEFAULT
	}
	return p.SingleDocs
}

var LarkDocConfig_WikiList_DEFAULT []*WikiDocConfig

func (p *LarkDocConfig) GetWikiList() (v []*WikiDocConfig) {
	if !p.IsSetWikiList() {
		return LarkDocConfig_WikiList_DEFAULT
	}
	return p.WikiList
}

func (p *LarkDocConfig) IsSetSingleDocs() bool {
	return p.SingleDocs != nil
}

func (p *LarkDocConfig) IsSetWikiList() bool {
	return p.WikiList != nil
}

func (p *LarkDocConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkDocConfig(%+v)", *p)
}

type WikiDocConfig struct {
	// 知识空间信息
	WikiSpace *WikiSpace `thrift:"WikiSpace,1,required" json:"wiki_space"`
	// 文档信息
	Docs []*WikiDoc `thrift:"Docs,2,required" json:"docs"`
}

func NewWikiDocConfig() *WikiDocConfig {
	return &WikiDocConfig{}
}

func (p *WikiDocConfig) InitDefault() {
}

var WikiDocConfig_WikiSpace_DEFAULT *WikiSpace

func (p *WikiDocConfig) GetWikiSpace() (v *WikiSpace) {
	if !p.IsSetWikiSpace() {
		return WikiDocConfig_WikiSpace_DEFAULT
	}
	return p.WikiSpace
}

func (p *WikiDocConfig) GetDocs() (v []*WikiDoc) {
	return p.Docs
}

func (p *WikiDocConfig) IsSetWikiSpace() bool {
	return p.WikiSpace != nil
}

func (p *WikiDocConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WikiDocConfig(%+v)", *p)
}

type WikiSpace struct {
	SpaceID   string `thrift:"SpaceID,1,required" json:"space_id"`
	SpaceName string `thrift:"SpaceName,2,required" json:"space_name"`
}

func NewWikiSpace() *WikiSpace {
	return &WikiSpace{}
}

func (p *WikiSpace) InitDefault() {
}

func (p *WikiSpace) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *WikiSpace) GetSpaceName() (v string) {
	return p.SpaceName
}

func (p *WikiSpace) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WikiSpace(%+v)", *p)
}

type WikiDoc struct {
	// 是否全选
	SelectAll *bool `thrift:"SelectAll,1,optional" json:"select_all"`
	// 链接
	Url string `thrift:"Url,2,required" json:"url"`
}

func NewWikiDoc() *WikiDoc {
	return &WikiDoc{}
}

func (p *WikiDoc) InitDefault() {
}

var WikiDoc_SelectAll_DEFAULT bool

func (p *WikiDoc) GetSelectAll() (v bool) {
	if !p.IsSetSelectAll() {
		return WikiDoc_SelectAll_DEFAULT
	}
	return *p.SelectAll
}

func (p *WikiDoc) GetUrl() (v string) {
	return p.Url
}

func (p *WikiDoc) IsSetSelectAll() bool {
	return p.SelectAll != nil
}

func (p *WikiDoc) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WikiDoc(%+v)", *p)
}

type SearchCodeRepoRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id" query:"space_id,required" `
	// 查询条件
	Query    *string `thrift:"Query,2,optional" json:"query" query:"query" `
	PageNum  int64   `thrift:"PageNum,3,required" json:"page_num" query:"page_num,required" `
	PageSize int64   `thrift:"PageSize,4,required" json:"page_size" query:"page_size,required" `
}

func NewSearchCodeRepoRequest() *SearchCodeRepoRequest {
	return &SearchCodeRepoRequest{}
}

func (p *SearchCodeRepoRequest) InitDefault() {
}

func (p *SearchCodeRepoRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var SearchCodeRepoRequest_Query_DEFAULT string

func (p *SearchCodeRepoRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return SearchCodeRepoRequest_Query_DEFAULT
	}
	return *p.Query
}

func (p *SearchCodeRepoRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *SearchCodeRepoRequest) GetPageSize() (v int64) {
	return p.PageSize
}

func (p *SearchCodeRepoRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *SearchCodeRepoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchCodeRepoRequest(%+v)", *p)
}

type SearchCodeRepoResponse struct {
	CodeRepos []*CodeRepo `thrift:"CodeRepos,1" json:"code_repos"`
	Total     int64       `thrift:"Total,2" json:"total"`
}

func NewSearchCodeRepoResponse() *SearchCodeRepoResponse {
	return &SearchCodeRepoResponse{}
}

func (p *SearchCodeRepoResponse) InitDefault() {
}

func (p *SearchCodeRepoResponse) GetCodeRepos() (v []*CodeRepo) {
	return p.CodeRepos
}

func (p *SearchCodeRepoResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *SearchCodeRepoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchCodeRepoResponse(%+v)", *p)
}

type SearchServiceRequest struct {
	SpaceID     string       `thrift:"SpaceID,1,required" json:"space_id" query:"space_id,required" `
	Query       *string      `thrift:"Query,2,optional" json:"query" query:"query" `
	ServiceType *ServiceType `thrift:"ServiceType,3,optional" json:"service_type" query:"service_type" `
	PageNum     int64        `thrift:"PageNum,4,required" json:"page_num" query:"page_num,required" `
	PageSize    int64        `thrift:"PageSize,5,required" json:"page_size" query:"page_size,required" `
}

func NewSearchServiceRequest() *SearchServiceRequest {
	return &SearchServiceRequest{}
}

func (p *SearchServiceRequest) InitDefault() {
}

func (p *SearchServiceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var SearchServiceRequest_Query_DEFAULT string

func (p *SearchServiceRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return SearchServiceRequest_Query_DEFAULT
	}
	return *p.Query
}

var SearchServiceRequest_ServiceType_DEFAULT ServiceType

func (p *SearchServiceRequest) GetServiceType() (v ServiceType) {
	if !p.IsSetServiceType() {
		return SearchServiceRequest_ServiceType_DEFAULT
	}
	return *p.ServiceType
}

func (p *SearchServiceRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *SearchServiceRequest) GetPageSize() (v int64) {
	return p.PageSize
}

func (p *SearchServiceRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *SearchServiceRequest) IsSetServiceType() bool {
	return p.ServiceType != nil
}

func (p *SearchServiceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchServiceRequest(%+v)", *p)
}

type SearchServiceResponse struct {
	Services []*Service `thrift:"Services,1" json:"services"`
	Total    int64      `thrift:"Total,2" json:"total"`
}

func NewSearchServiceResponse() *SearchServiceResponse {
	return &SearchServiceResponse{}
}

func (p *SearchServiceResponse) InitDefault() {
}

func (p *SearchServiceResponse) GetServices() (v []*Service) {
	return p.Services
}

func (p *SearchServiceResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *SearchServiceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchServiceResponse(%+v)", *p)
}

type ListCodeRepoRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id"`
	// 查询条件
	Query         *string         `thrift:"Query,2,optional" json:"query"`
	PageNum       *int64          `thrift:"PageNum,3,optional" json:"page_num"`
	PageSize      *int64          `thrift:"PageSize,4,optional" json:"page_size"`
	Creators      []string        `thrift:"Creators,5,optional" json:"creators"`
	ProcessStatus []ProcessStatus `thrift:"ProcessStatus,6,optional" json:"process_status"`
}

func NewListCodeRepoRequest() *ListCodeRepoRequest {
	return &ListCodeRepoRequest{}
}

func (p *ListCodeRepoRequest) InitDefault() {
}

func (p *ListCodeRepoRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var ListCodeRepoRequest_Query_DEFAULT string

func (p *ListCodeRepoRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return ListCodeRepoRequest_Query_DEFAULT
	}
	return *p.Query
}

var ListCodeRepoRequest_PageNum_DEFAULT int64

func (p *ListCodeRepoRequest) GetPageNum() (v int64) {
	if !p.IsSetPageNum() {
		return ListCodeRepoRequest_PageNum_DEFAULT
	}
	return *p.PageNum
}

var ListCodeRepoRequest_PageSize_DEFAULT int64

func (p *ListCodeRepoRequest) GetPageSize() (v int64) {
	if !p.IsSetPageSize() {
		return ListCodeRepoRequest_PageSize_DEFAULT
	}
	return *p.PageSize
}

var ListCodeRepoRequest_Creators_DEFAULT []string

func (p *ListCodeRepoRequest) GetCreators() (v []string) {
	if !p.IsSetCreators() {
		return ListCodeRepoRequest_Creators_DEFAULT
	}
	return p.Creators
}

var ListCodeRepoRequest_ProcessStatus_DEFAULT []ProcessStatus

func (p *ListCodeRepoRequest) GetProcessStatus() (v []ProcessStatus) {
	if !p.IsSetProcessStatus() {
		return ListCodeRepoRequest_ProcessStatus_DEFAULT
	}
	return p.ProcessStatus
}

func (p *ListCodeRepoRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *ListCodeRepoRequest) IsSetPageNum() bool {
	return p.PageNum != nil
}

func (p *ListCodeRepoRequest) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *ListCodeRepoRequest) IsSetCreators() bool {
	return p.Creators != nil
}

func (p *ListCodeRepoRequest) IsSetProcessStatus() bool {
	return p.ProcessStatus != nil
}

func (p *ListCodeRepoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCodeRepoRequest(%+v)", *p)
}

type ListCodeRepoResponse struct {
	CodeRepos []*CodeRepo `thrift:"CodeRepos,1" json:"code_repos"`
	Total     int64       `thrift:"Total,2" json:"total"`
}

func NewListCodeRepoResponse() *ListCodeRepoResponse {
	return &ListCodeRepoResponse{}
}

func (p *ListCodeRepoResponse) InitDefault() {
}

func (p *ListCodeRepoResponse) GetCodeRepos() (v []*CodeRepo) {
	return p.CodeRepos
}

func (p *ListCodeRepoResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListCodeRepoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCodeRepoResponse(%+v)", *p)
}

type DeleteCodeRepoRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id" form:"space_id,required" `
	// 代码仓库
	CodeRepos []*CodeRepo `thrift:"CodeRepos,2,required" json:"code_repos" form:"code_repos,required" `
}

func NewDeleteCodeRepoRequest() *DeleteCodeRepoRequest {
	return &DeleteCodeRepoRequest{}
}

func (p *DeleteCodeRepoRequest) InitDefault() {
}

func (p *DeleteCodeRepoRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *DeleteCodeRepoRequest) GetCodeRepos() (v []*CodeRepo) {
	return p.CodeRepos
}

func (p *DeleteCodeRepoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCodeRepoRequest(%+v)", *p)
}

type DeleteCodeRepoResponse struct {
}

func NewDeleteCodeRepoResponse() *DeleteCodeRepoResponse {
	return &DeleteCodeRepoResponse{}
}

func (p *DeleteCodeRepoResponse) InitDefault() {
}

func (p *DeleteCodeRepoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCodeRepoResponse(%+v)", *p)
}

type UploadCodeRepoRequest struct {
	// 项目空间 ID
	SpaceID   string      `thrift:"SpaceID,1,required" json:"space_id"`
	CodeRepos []*CodeRepo `thrift:"CodeRepos,2,required" json:"code_repos"`
}

func NewUploadCodeRepoRequest() *UploadCodeRepoRequest {
	return &UploadCodeRepoRequest{}
}

func (p *UploadCodeRepoRequest) InitDefault() {
}

func (p *UploadCodeRepoRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *UploadCodeRepoRequest) GetCodeRepos() (v []*CodeRepo) {
	return p.CodeRepos
}

func (p *UploadCodeRepoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadCodeRepoRequest(%+v)", *p)
}

type UploadCodeRepoResponse struct {
}

func NewUploadCodeRepoResponse() *UploadCodeRepoResponse {
	return &UploadCodeRepoResponse{}
}

func (p *UploadCodeRepoResponse) InitDefault() {
}

func (p *UploadCodeRepoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadCodeRepoResponse(%+v)", *p)
}

type ListServiceRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id"`
	// 查询条件
	Query         *string         `thrift:"Query,2,optional" json:"query"`
	PageNum       int64           `thrift:"PageNum,3,required" json:"page_num"`
	PageSize      int64           `thrift:"PageSize,4,required" json:"page_size"`
	Creators      []string        `thrift:"Creators,5,optional" json:"creators"`
	ProcessStatus []ProcessStatus `thrift:"ProcessStatus,6,optional" json:"process_status"`
}

func NewListServiceRequest() *ListServiceRequest {
	return &ListServiceRequest{}
}

func (p *ListServiceRequest) InitDefault() {
}

func (p *ListServiceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var ListServiceRequest_Query_DEFAULT string

func (p *ListServiceRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return ListServiceRequest_Query_DEFAULT
	}
	return *p.Query
}

func (p *ListServiceRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListServiceRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListServiceRequest_Creators_DEFAULT []string

func (p *ListServiceRequest) GetCreators() (v []string) {
	if !p.IsSetCreators() {
		return ListServiceRequest_Creators_DEFAULT
	}
	return p.Creators
}

var ListServiceRequest_ProcessStatus_DEFAULT []ProcessStatus

func (p *ListServiceRequest) GetProcessStatus() (v []ProcessStatus) {
	if !p.IsSetProcessStatus() {
		return ListServiceRequest_ProcessStatus_DEFAULT
	}
	return p.ProcessStatus
}

func (p *ListServiceRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *ListServiceRequest) IsSetCreators() bool {
	return p.Creators != nil
}

func (p *ListServiceRequest) IsSetProcessStatus() bool {
	return p.ProcessStatus != nil
}

func (p *ListServiceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListServiceRequest(%+v)", *p)
}

type ListServiceResponse struct {
	Services []*Service `thrift:"Services,1" json:"services"`
	Total    int64      `thrift:"Total,2" json:"total"`
}

func NewListServiceResponse() *ListServiceResponse {
	return &ListServiceResponse{}
}

func (p *ListServiceResponse) InitDefault() {
}

func (p *ListServiceResponse) GetServices() (v []*Service) {
	return p.Services
}

func (p *ListServiceResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListServiceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListServiceResponse(%+v)", *p)
}

type DeleteServiceRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id" form:"space_id,required" `
	// 服务
	Services []*Service `thrift:"Services,2,required" json:"services" form:"services,required" `
}

func NewDeleteServiceRequest() *DeleteServiceRequest {
	return &DeleteServiceRequest{}
}

func (p *DeleteServiceRequest) InitDefault() {
}

func (p *DeleteServiceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *DeleteServiceRequest) GetServices() (v []*Service) {
	return p.Services
}

func (p *DeleteServiceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteServiceRequest(%+v)", *p)
}

type DeleteServiceResponse struct {
}

func NewDeleteServiceResponse() *DeleteServiceResponse {
	return &DeleteServiceResponse{}
}

func (p *DeleteServiceResponse) InitDefault() {
}

func (p *DeleteServiceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteServiceResponse(%+v)", *p)
}

type UploadServiceRequest struct {
	// 项目空间 ID
	SpaceID  string     `thrift:"SpaceID,1,required" json:"space_id"`
	Services []*Service `thrift:"Services,2,required" json:"services"`
}

func NewUploadServiceRequest() *UploadServiceRequest {
	return &UploadServiceRequest{}
}

func (p *UploadServiceRequest) InitDefault() {
}

func (p *UploadServiceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *UploadServiceRequest) GetServices() (v []*Service) {
	return p.Services
}

func (p *UploadServiceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceRequest(%+v)", *p)
}

type UploadServiceResponse struct {
}

func NewUploadServiceResponse() *UploadServiceResponse {
	return &UploadServiceResponse{}
}

func (p *UploadServiceResponse) InitDefault() {
}

func (p *UploadServiceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceResponse(%+v)", *p)
}

type SearchMeegoSpaceRequest struct {
	Query   string `thrift:"Query,1,required" json:"query" query:"query,required" `
	SpaceID string `thrift:"SpaceID,2,required" json:"space_id" query:"space_id,required" `
}

func NewSearchMeegoSpaceRequest() *SearchMeegoSpaceRequest {
	return &SearchMeegoSpaceRequest{}
}

func (p *SearchMeegoSpaceRequest) InitDefault() {
}

func (p *SearchMeegoSpaceRequest) GetQuery() (v string) {
	return p.Query
}

func (p *SearchMeegoSpaceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *SearchMeegoSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchMeegoSpaceRequest(%+v)", *p)
}

type SearchMeegoSpaceResponse struct {
	MeegoSpaces []*MeegoSpace `thrift:"MeegoSpaces,1" json:"meego_spaces"`
}

func NewSearchMeegoSpaceResponse() *SearchMeegoSpaceResponse {
	return &SearchMeegoSpaceResponse{}
}

func (p *SearchMeegoSpaceResponse) InitDefault() {
}

func (p *SearchMeegoSpaceResponse) GetMeegoSpaces() (v []*MeegoSpace) {
	return p.MeegoSpaces
}

func (p *SearchMeegoSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchMeegoSpaceResponse(%+v)", *p)
}

type MeegoSpace struct {
	SpaceID    string `thrift:"SpaceID,1" json:"space_id"`
	SpaceName  string `thrift:"SpaceName,2" json:"space_name"`
	SimpleName string `thrift:"SimpleName,3" json:"single_name"`
	Url        string `thrift:"Url,4" json:"url"`
	// 是否上传
	IsUploaded bool `thrift:"IsUploaded,5" json:"is_uploaded"`
}

func NewMeegoSpace() *MeegoSpace {
	return &MeegoSpace{}
}

func (p *MeegoSpace) InitDefault() {
}

func (p *MeegoSpace) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *MeegoSpace) GetSpaceName() (v string) {
	return p.SpaceName
}

func (p *MeegoSpace) GetSimpleName() (v string) {
	return p.SimpleName
}

func (p *MeegoSpace) GetUrl() (v string) {
	return p.Url
}

func (p *MeegoSpace) GetIsUploaded() (v bool) {
	return p.IsUploaded
}

func (p *MeegoSpace) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MeegoSpace(%+v)", *p)
}

type GetPlatformConfigRequest struct {
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id" query:"space_id,required" `
}

func NewGetPlatformConfigRequest() *GetPlatformConfigRequest {
	return &GetPlatformConfigRequest{}
}

func (p *GetPlatformConfigRequest) InitDefault() {
}

func (p *GetPlatformConfigRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *GetPlatformConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlatformConfigRequest(%+v)", *p)
}

type GetPlatformConfigResponse struct {
	PlatformConfig *PlatformConfig `thrift:"PlatformConfig,1" json:"platform_config"`
}

func NewGetPlatformConfigResponse() *GetPlatformConfigResponse {
	return &GetPlatformConfigResponse{}
}

func (p *GetPlatformConfigResponse) InitDefault() {
}

var GetPlatformConfigResponse_PlatformConfig_DEFAULT *PlatformConfig

func (p *GetPlatformConfigResponse) GetPlatformConfig() (v *PlatformConfig) {
	if !p.IsSetPlatformConfig() {
		return GetPlatformConfigResponse_PlatformConfig_DEFAULT
	}
	return p.PlatformConfig
}

func (p *GetPlatformConfigResponse) IsSetPlatformConfig() bool {
	return p.PlatformConfig != nil
}

func (p *GetPlatformConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlatformConfigResponse(%+v)", *p)
}

type UpdatePlatformConfigRequest struct {
	SpaceID     string        `thrift:"SpaceID,1,required" json:"space_id"`
	MeegoSpaces []*MeegoSpace `thrift:"meegoSpaces,2,required" json:"meego_spaces"`
	OperateType OperateType   `thrift:"OperateType,3,required" json:"operate_type"`
}

func NewUpdatePlatformConfigRequest() *UpdatePlatformConfigRequest {
	return &UpdatePlatformConfigRequest{}
}

func (p *UpdatePlatformConfigRequest) InitDefault() {
}

func (p *UpdatePlatformConfigRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *UpdatePlatformConfigRequest) GetMeegoSpaces() (v []*MeegoSpace) {
	return p.MeegoSpaces
}

func (p *UpdatePlatformConfigRequest) GetOperateType() (v OperateType) {
	return p.OperateType
}

func (p *UpdatePlatformConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePlatformConfigRequest(%+v)", *p)
}

type UpdatePlatformConfigResponse struct {
}

func NewUpdatePlatformConfigResponse() *UpdatePlatformConfigResponse {
	return &UpdatePlatformConfigResponse{}
}

func (p *UpdatePlatformConfigResponse) InitDefault() {
}

func (p *UpdatePlatformConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePlatformConfigResponse(%+v)", *p)
}
