namespace go nextagent

struct CodeRepo {
    1: required string RepoID (go.tag = "json:\"repo_id\""),
    2: required string RepoName  (go.tag = "json:\"repo_name\""),
    3: bool IsUploaded (go.tag = "json:\"is_uploaded\""),
    4: optional string Desc (go.tag = "json:\"desc\""),
    5: optional string AvatarUrl (go.tag = "json:\"avatar_url\""),
    6: required string Url (go.tag = "json:\"url\""),
    7: ProcessStatus ProcessStatus (go.tag = "json:\"process_status\""),
}

struct Service {
  // 项目唯一标识
  1: required string UniqueId (go.tag = "json:\"unique_id\""),

  // 项目类型
  2: required ServiceType Type (go.tag = "json:\"type\""),

  // 项目名称
  3: required string Name (go.tag = "json:\"name\"")

  // 控制面
  4: required ControlPlane ControlPlane (go.tag = "json:\"control_plane\"")

  // 是否上传
  5: bool IsUploaded (go.tag = "json:\"is_uploaded\"")

  6: ProcessStatus ProcessStatus (go.tag = "json:\"process_status\""),

  7: required string Url (go.tag = "json:\"url\""),
}

struct PlatformConfig {
  1: optional list<MeegoSpace> MeegoSpaceList (go.tag="json:\"meego_space_list\"")
}

enum ControlPlane {
      // UNSPECIFIED 在阶段模板配置里，可以额外用于未指定控制面（自由流水线）
      CONTROL_PLANE_UNSPECIFIED = 0;
      CONTROL_PLANE_CN = 1;
      CONTROL_PLANE_I18N = 2;
      // deprecated 沿用 ByteCycle US-TTP 方案使用的枚举值
      CONTROL_PLANE_TTP = 3;
      CONTROL_PLANE_EU_TTP = 4;
      CONTROL_PLANE_US_TTP = 5;
      // I18N non TT
      CONTROL_PLANE_I18N_BD = 6;
}

enum ServiceType {
      SERVICE_TYPE_UNSPECIFIED = 0;
      // TCE
      SERVICE_TYPE_TCE = 1;
      // ByteFaaS
      SERVICE_TYPE_FAAS = 2;
      // Cronjob
      SERVICE_TYPE_CRONJOB = 3;
      // 新版 Web
      SERVICE_TYPE_WEB = 4;
      // 跨端
      SERVICE_TYPE_HYBRID = 5;
      // 组件/模块
      SERVICE_TYPE_LIBRARY = 6;
      // 新版 NodeJS
      SERVICE_TYPE_NODEJS = 7;
      // 新版 Monorepo
      SERVICE_TYPE_MONOREPO = 8;
      // sidecar
      SERVICE_TYPE_SIDECAR = 9;
      // webapp
      SERVICE_TYPE_WEB_APP = 10;
      // 旧版web
      SERVICE_TYPE_OLD_WEB = 11;
      // TCC
      SERVICE_TYPE_TCC = 12;
      // 自定义项目类型
      SERVICE_TYPE_CUSTOM = 100;
}

typedef string ProcessStatus
const ProcessStatus ProcessStatusSuccess = "success"
const ProcessStatus ProcessStatusFailed = "failed"
const ProcessStatus ProcessStatusProcessing = "processing"

struct LarkDocConfig {
    1: optional list<string> SingleDocs (go.tag="json:\"single_docs\""),           // 单篇文档信息
    2: optional list<WikiDocConfig> WikiList (go.tag="json:\"wiki_list\"")      // 导入的知识库列表
}

struct WikiDocConfig {
    1: required WikiSpace WikiSpace (go.tag="json:\"wiki_space\"")  // 知识空间信息
    2: required list<WikiDoc> Docs  (go.tag="json:\"docs\"")  // 文档信息
}

struct WikiSpace {
   1: required string SpaceID (go.tag = "json:\"space_id\"")
   2: required string SpaceName (go.tag = "json:\"space_name\"")
}

struct WikiDoc {
    1: optional bool SelectAll (go.tag="json:\"select_all\""),   // 是否全选
    2: required string Url (go.tag="json:\"url\"")       // 链接
}

struct SearchCodeRepoRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\"", api.query = "space_id"),            // 项目空间 ID
     2: optional string Query (go.tag = "json:\"query\"", api.query = "query"),              // 查询条件
     3: required i64 PageNum (go.tag = "json:\"page_num\"", api.query = "page_num"),
     4: required i64 PageSize (go.tag = "json:\"page_size\"", api.query = "page_size"),
}

struct SearchCodeRepoResponse {
    1: list<CodeRepo> CodeRepos (go.tag = "json:\"code_repos\"")
    2: i64 Total (go.tag = "json:\"total\""),
}

struct SearchServiceRequest {
   1: required string SpaceID (go.tag = "json:\"space_id\"", api.query="space_id")
   2: optional string Query (go.tag = "json:\"query\"", api.query="query")
   3: optional ServiceType ServiceType (go.tag = "json:\"service_type\"", api.query="service_type")
   4: required i64 PageNum (go.tag = "json:\"page_num\"", api.query = "page_num"),
   5: required i64 PageSize (go.tag = "json:\"page_size\"", api.query = "page_size"),
}

struct SearchServiceResponse {
    1: list<Service> Services (go.tag = "json:\"services\"")
    2: i64 Total (go.tag = "json:\"total\""),
}

struct ListCodeRepoRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\""),            // 项目空间 ID
     2: optional string Query (go.tag = "json:\"query\""),              // 查询条件
     3: optional i64 PageNum (go.tag = "json:\"page_num\""),
     4: optional i64 PageSize (go.tag = "json:\"page_size\""),
     5: optional list<string> Creators (go.tag = "json:\"creators\""),
     6: optional list<ProcessStatus> ProcessStatus (go.tag = "json:\"process_status\""),
}

struct ListCodeRepoResponse {
    1: list<CodeRepo> CodeRepos (go.tag = "json:\"code_repos\"")
    2: i64 Total (go.tag = "json:\"total\""),
}

struct DeleteCodeRepoRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\"", api.body = "space_id"),            // 项目空间 ID
     2: required list<CodeRepo> CodeRepos (go.tag = "json:\"code_repos\"", api.body = "code_repos")           // 代码仓库
}

struct DeleteCodeRepoResponse {}

struct UploadCodeRepoRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\""),            // 项目空间 ID
     2: required list<CodeRepo> CodeRepos (go.tag = "json:\"code_repos\"")
}

struct UploadCodeRepoResponse {}

struct ListServiceRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\""),            // 项目空间 ID
     2: optional string Query (go.tag = "json:\"query\""),              // 查询条件
     3: required i64 PageNum (go.tag = "json:\"page_num\""),
     4: required i64 PageSize (go.tag = "json:\"page_size\""),
     5: optional list<string> Creators (go.tag = "json:\"creators\""),
     6: optional list<ProcessStatus> ProcessStatus (go.tag = "json:\"process_status\""),
}

struct ListServiceResponse {
    1: list<Service> Services (go.tag = "json:\"services\"")
    2: i64 Total (go.tag = "json:\"total\""),
}

struct DeleteServiceRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\"", api.body = "space_id"),            // 项目空间 ID
     2: required list<Service> Services (go.tag = "json:\"services\"", api.body = "services")           // 服务
}

struct DeleteServiceResponse {}

struct UploadServiceRequest {
     1: required string SpaceID (go.tag = "json:\"space_id\""),            // 项目空间 ID
     2: required list<Service> Services (go.tag = "json:\"services\"")
}

struct UploadServiceResponse {}

struct SearchMeegoSpaceRequest {
   1: required string Query (go.tag = "json:\"query\"", api.query="query")
   2: required string SpaceID (go.tag = "json:\"space_id\"", api.query="space_id");
}

struct SearchMeegoSpaceResponse {
    1: list<MeegoSpace> MeegoSpaces (go.tag = "json:\"meego_spaces\"")
}

struct MeegoSpace {
    1: string SpaceID (go.tag = "json:\"space_id\"");
    2: string SpaceName (go.tag = "json:\"space_name\"");
    3: string SimpleName (go.tag = "json:\"single_name\"");
    4: string Url (go.tag = "json:\"url\"");
    // 是否上传
    5: bool IsUploaded (go.tag = "json:\"is_uploaded\"")
}

struct GetPlatformConfigRequest {
    1: required string SpaceID (go.tag = "json:\"space_id\"", api.query="space_id")
}

struct GetPlatformConfigResponse {
    1: PlatformConfig PlatformConfig (go.tag = "json:\"platform_config\"")
}

typedef  string OperateType
const OperateType OperateTypeAdd  = "add"
const OperateType OperateTypeDelete  = "delete"

struct UpdatePlatformConfigRequest {
    1: required string SpaceID (go.tag = "json:\"space_id\"")
    2: required list<MeegoSpace> meegoSpaces (go.tag = "json:\"meego_spaces\"")
    3: required OperateType OperateType (go.tag = "json:\"operate_type\"")
}

struct UpdatePlatformConfigResponse {}