namespace go nextagent

include "permission.thrift"
include "dev_resource.thrift"
include "share.thrift"

typedef string SpaceStatus

const SpaceStatus SpaceStatusActive = "active"
const SpaceStatus SpaceStatusInactive = "inactive"
const SpaceStatus SpaceStatusDeleted = "deleted"
const SpaceStatus SpaceStatusUninit = "uninit"

typedef string SpaceType

const SpaceType SpaceTypePersonal = "personal" // 个人空间
const SpaceType SpaceTypeProject = "project" // 项目空间

// 数据结构定义
struct Space {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string Name (go.tag = "json:\"name\""),
    3: optional string NameEN (go.tag = "json:\"name_en,omitempty\""),
    4: optional string Description (go.tag = "json:\"description,omitempty\""),
    5: required string Creator (go.tag = "json:\"creator\""),
    6: required SpaceType Type (go.tag = "json:\"type\""), // 空间类型
    7: required SpaceStatus Status (go.tag = "json:\"status\""),
    8: required string CreatedAt (go.tag = "json:\"created_at\""),
    9: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    10: optional string DeletedAt (go.tag = "json:\"deleted_at,omitempty\""),
    11: optional list<SpaceMember> Members (go.tag = "json:\"members,omitempty\"")  // 空间用户列表(可选)
    12: required string DataSetID (go.tag = "json:\"dataset_id\""),
    13: optional SpaceConfig SpaceConfig (go.tag = "json:\"space_config,omitempty\""),
    14: optional list<permission.PermissionAction> PermissionActions (go.tag = "json:\"permission_actions,omitempty\""), // 权限列表
}

struct SpaceMember {
    1: required permission.PermissionType Type (go.tag = "json:\"type\""), // member类型
    2: required string Name (go.tag = "json:\"name\""),
    3: required permission.PermissionRole Role (go.tag = "json:\"role\""),
}

struct CreateSpaceRequest {
    1: required string Name (go.tag = "json:\"name\""),
    2: optional string NameEN (go.tag = "json:\"name_en,omitempty\""),
    3: optional string Description (go.tag = "json:\"description,omitempty\""),
    4: optional SpaceConfig SpaceConfig (go.tag = "json:\"space_config,omitempty\""),
}

struct CreateSpaceResponse {
    1: required Space Space (go.tag = "json:\"space\"")
}

struct CreatePersonalSpaceRequest {
    1: required string UserName (go.tag = "json:\"user_name\""),
}

struct CreatePersonalSpaceResponse {
    1: required Space Space (go.tag = "json:\"space\"") // space
    2: optional permission.Resource Resource (go.tag = "json:\"resource,omitempty\"") // resource
}

struct SpaceBaseConfig {
    # 空间 session 默认可见性，默认 false 不可见，调整后只对后续新创建的 session 生效，之前创建的按照之前的配置决定
    1: optional bool SessionVisibility (go.tag = "json:\"session_visibility\"")
}

struct SpaceConfig {
    1: optional SpaceBaseConfig BaseConfig (go.tag = "json:\"base_config\"")
}

struct UpdateSpaceRequest {
    1: required string SpaceId (go.tag = "json:\"space_id\""),
    2: optional string Name (go.tag = "json:\"name,omitempty\""),
    3: optional string NameEN (go.tag = "json:\"name_en,omitempty\""),
    4: optional string Description (go.tag = "json:\"description,omitempty\""),
    5: optional SpaceConfig SpaceConfig (go.tag = "json:\"space_config,omitempty\""),
}

struct UpdateSpaceResponse {
    1: required Space Space (go.tag = "json:\"space\"")
}

struct GetSpaceRequest {
    1: optional string SpaceId (api.query="space_id");
    2: optional permission.ResourceType Type (api.query="type");
    3: optional string ExternalID (api.query="external_id");
    4: optional bool NeedMembers (api.query="need_members");
}

struct GetSpaceResponse {
    1: required Space Space (go.tag = "json:\"space\""),
    2: optional bool HavePermission (go.tag = "json:\"have_permission\""),
}

struct DeleteSpaceRequest {
    1: required string SpaceId (api.body = "space_id", go.tag = "json:\"space_id\""),
}

struct DeleteSpaceResponse {
    1: required bool Success (go.tag = "json:\"success\"")
}

struct ListUserSpacesRequest {
    1: optional string StartID (api.query = "start_id"),
    2: optional i64 Limit (api.query = "limit"),
    3: optional SpaceType Type (api.query = "type"),
}

struct ListUserSpacesResponse {
    1: required list<Space> Spaces (go.tag = "json:\"spaces\""),
    2: required string NextID (go.tag = "json:\"next_id\""),
}

struct ListAllSpacesRequest {
    1: required i64 PageNum (api.query = "page_num"),
    2: required i64 PageSize (api.query = "page_size"),
}

struct ListAllSpacesResponse {
    1: required list<Space> Spaces (go.tag = "json:\"spaces\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct AddSpaceMemberRequest {
    1: required string SpaceId (go.tag = "json:\"space_id\""),
    2: required list<SpaceMember> Members (go.tag = "json:\"members\""), // 成员列表

}

struct AddSpaceMemberResponse {
    1: required bool Success (go.tag = "json:\"success\"")
}

struct RemoveSpaceMemberRequest {
    1: required string SpaceId (api.body = "space_id", go.tag = "json:\"space_id\""),
    2: required list<SpaceMember> Members (api.body = "members", go.tag = "json:\"members\""), // 成员列表
}

struct RemoveSpaceMemberResponse {
    1: required bool Success (go.tag = "json:\"success\"")
}

struct ListSpaceMembersRequest {
    1: required string SpaceId (api.query="space_id");
    2: required i64 PageNum (api.query = "page_num");
    3: required i64 PageSize (api.query = "page_size");
    4: optional permission.PermissionRole Role (api.query = "role"); // 过滤角色
    5: optional permission.PermissionType Type (api.query = "type"); // 过滤类型
}

struct ListSpaceMembersResponse {
    1: required list<SpaceMember> Members (go.tag = "json:\"members\""), 
    2: required i64 Total (go.tag = "json:\"total\"")
}

struct  InitSpaceRequest {
    1: required string SpaceID (go.tag="json:\"space_id\""),            // 项目空间 ID
    2: required BasicInfo Basic (go.tag="json:\"basic\""),           // 空间基本信息
    3: optional list<SpaceMember> Members (go.tag="json:\"members\""), // 空间成员
    4: optional dev_resource.LarkDocConfig LarkDocConfig (go.tag="json:\"lark_doc_config\""),        // 文档信息
    5: optional list<dev_resource.CodeRepo> Repos (go.tag="json:\"repos\""),      // 仓库信息
    6: optional list<dev_resource.Service> Services (go.tag="json:\"services\""),    // 项目信息
    7: optional dev_resource.PlatformConfig PlatformConfig (go.tag="json:\"platform_config\"")   // 其余空间配置信息
}

struct InitSpaceResponse {}

struct BasicInfo {
    1: required StringInMultiLang Name (go.tag = "json:\"name\""),
    2: optional StringInMultiLang Description (go.tag = "json:\"description,omitempty\""),
}

struct StringInMultiLang {
    1: string Cn (go.tag="json:\"cn\"")
    2: string En (go.tag="json:\"en\"")
}